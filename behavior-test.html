<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行为系统测试 - 功夫狗3D</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #87CEEB, #98FB98);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            color: #4CAF50;
            margin-top: 0;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status-display {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .emotion-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .emotion-control {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .emotion-control label {
            font-weight: bold;
            color: #333;
        }
        
        .emotion-control input[type="range"] {
            width: 100%;
        }
        
        .emotion-value {
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 功夫狗行为系统测试</h1>
        
        <!-- 3D场景 -->
        <div class="test-section">
            <h2>🐕 3D场景</h2>
            <iframe src="http://localhost:3005/" title="功夫狗3D场景"></iframe>
        </div>
        
        <!-- 行为触发测试 -->
        <div class="test-section">
            <h2>🎬 行为触发测试</h2>
            <div class="button-group">
                <button onclick="triggerBehavior('idle')">待机行为</button>
                <button onclick="triggerBehavior('greeting')">问候行为</button>
                <button onclick="triggerBehavior('showOff')">炫技行为</button>
                <button onclick="triggerBehavior('contemplation')">思考行为</button>
                <button onclick="triggerBehavior('energetic')">活跃行为</button>
                <button onclick="triggerBehavior('alert')">警觉行为</button>
            </div>
            <div class="button-group">
                <button onclick="performKungfuDemo()">🥋 功夫表演</button>
                <button onclick="toggleAutoBehavior()">🤖 切换自动行为</button>
            </div>
        </div>

        <!-- 身体部位交互测试 -->
        <div class="test-section">
            <h2>🎯 身体部位交互测试</h2>
            <div class="button-group">
                <button onclick="testBodyPartClick('head')">🐕 点击头部</button>
                <button onclick="testBodyPartClick('body')">🐕 点击身体</button>
                <button onclick="testBodyPartClick('legs')">🐕 点击腿部</button>
                <button onclick="testBodyPartClick('feet')">🐕 点击脚部</button>
            </div>
            <div class="button-group">
                <button onclick="testAllBodyParts()">🔄 测试所有部位</button>
            </div>
            <p style="font-size: 12px; color: #666; margin-top: 10px;">
                💡 提示：现在身体部位点击会触发相应的行为而不是简单动作
            </p>
        </div>
        
        <!-- 情绪状态控制 -->
        <div class="test-section">
            <h2>😊 情绪状态控制</h2>
            <div class="emotion-controls">
                <div class="emotion-control">
                    <label for="happiness">快乐度 (Happiness)</label>
                    <input type="range" id="happiness" min="0" max="1" step="0.1" value="0.7" 
                           onchange="setEmotion('happiness', this.value)">
                    <div class="emotion-value" id="happiness-value">0.7</div>
                </div>
                <div class="emotion-control">
                    <label for="energy">精力 (Energy)</label>
                    <input type="range" id="energy" min="0" max="1" step="0.1" value="0.8" 
                           onchange="setEmotion('energy', this.value)">
                    <div class="emotion-value" id="energy-value">0.8</div>
                </div>
                <div class="emotion-control">
                    <label for="alertness">警觉性 (Alertness)</label>
                    <input type="range" id="alertness" min="0" max="1" step="0.1" value="0.6" 
                           onchange="setEmotion('alertness', this.value)">
                    <div class="emotion-value" id="alertness-value">0.6</div>
                </div>
                <div class="emotion-control">
                    <label for="friendliness">友好度 (Friendliness)</label>
                    <input type="range" id="friendliness" min="0" max="1" step="0.1" value="0.9" 
                           onchange="setEmotion('friendliness', this.value)">
                    <div class="emotion-value" id="friendliness-value">0.9</div>
                </div>
            </div>
        </div>
        
        <!-- 系统状态显示 -->
        <div class="test-section">
            <h2>📊 系统状态</h2>
            <div class="button-group">
                <button onclick="updateStatus()">🔄 刷新状态</button>
                <button onclick="clearStatus()">🗑️ 清空显示</button>
            </div>
            <div class="status-display" id="status-display">
点击"刷新状态"查看行为系统状态...
            </div>
        </div>
    </div>

    <script>
        // 获取iframe中的window对象
        function getIframeWindow() {
            const iframe = document.querySelector('iframe');
            return iframe.contentWindow;
        }
        
        // 等待iframe加载完成
        function waitForIframe(callback) {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow && iframe.contentWindow.triggerBehavior) {
                callback();
            } else {
                setTimeout(() => waitForIframe(callback), 1000);
            }
        }
        
        // 触发行为
        function triggerBehavior(behaviorName) {
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.triggerBehavior) {
                    const result = iframeWindow.triggerBehavior(behaviorName);
                    addToStatus(`触发行为: ${behaviorName} - ${result ? '成功' : '失败'}`);
                } else {
                    addToStatus(`错误: 无法访问行为系统`);
                }
            });
        }
        
        // 功夫表演
        function performKungfuDemo() {
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.performKungfuDemo) {
                    iframeWindow.performKungfuDemo();
                    addToStatus(`开始功夫表演`);
                } else {
                    addToStatus(`错误: 无法访问功夫表演功能`);
                }
            });
        }
        
        // 切换自动行为
        function toggleAutoBehavior() {
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.toggleAutoBehavior) {
                    const result = iframeWindow.toggleAutoBehavior();
                    addToStatus(`自动行为: ${result ? '启用' : '禁用'}`);
                } else {
                    addToStatus(`错误: 无法访问自动行为控制`);
                }
            });
        }
        
        // 设置情绪状态
        function setEmotion(emotion, value) {
            const numValue = parseFloat(value);
            document.getElementById(`${emotion}-value`).textContent = numValue.toFixed(1);
            
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.setEmotionalState) {
                    const result = iframeWindow.setEmotionalState(emotion, numValue);
                    addToStatus(`设置情绪 ${emotion}: ${numValue} - ${result ? '成功' : '失败'}`);
                } else {
                    addToStatus(`错误: 无法访问情绪控制`);
                }
            });
        }
        
        // 更新状态显示
        function updateStatus() {
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.getBehaviorStatus) {
                    const status = iframeWindow.getBehaviorStatus();
                    if (status) {
                        addToStatus('=== 行为系统状态 ===');
                        addToStatus(`当前行为: ${status.currentBehavior || '无'}`);
                        addToStatus(`正在执行: ${status.isExecuting ? '是' : '否'}`);
                        addToStatus(`队列长度: ${status.queueLength}`);
                        addToStatus(`自动行为: ${status.autoBehaviorEnabled ? '启用' : '禁用'}`);
                        addToStatus('情绪状态:');
                        for (const [emotion, value] of Object.entries(status.emotionalState)) {
                            addToStatus(`  ${emotion}: ${value.toFixed(2)}`);
                        }
                        addToStatus(`行为历史: ${status.behaviorHistory.join(', ')}`);
                        addToStatus('==================');
                    } else {
                        addToStatus('错误: 无法获取行为系统状态');
                    }
                } else {
                    addToStatus('错误: 无法访问状态查询功能');
                }
            });
        }
        
        // 添加状态信息
        function addToStatus(message) {
            const display = document.getElementById('status-display');
            const timestamp = new Date().toLocaleTimeString();
            display.textContent += `[${timestamp}] ${message}\n`;
            display.scrollTop = display.scrollHeight;
        }
        
        // 清空状态显示
        function clearStatus() {
            document.getElementById('status-display').textContent = '';
        }

        // 身体部位交互测试
        function testBodyPartClick(bodyPart) {
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.testBodyPartClick) {
                    const result = iframeWindow.testBodyPartClick(bodyPart);
                    addToStatus(`测试身体部位点击: ${bodyPart} - ${result ? '成功' : '失败'}`);
                } else {
                    addToStatus(`错误: 无法访问身体部位测试功能`);
                }
            });
        }

        function testAllBodyParts() {
            waitForIframe(() => {
                const iframeWindow = getIframeWindow();
                if (iframeWindow && iframeWindow.testAllBodyParts) {
                    iframeWindow.testAllBodyParts();
                    addToStatus(`开始测试所有身体部位（每3秒一个）`);
                } else {
                    addToStatus(`错误: 无法访问身体部位批量测试功能`);
                }
            });
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            addToStatus('行为系统测试页面已加载');
            addToStatus('等待3D场景加载完成...');
            
            // 定期检查iframe是否加载完成
            const checkInterval = setInterval(() => {
                const iframe = document.querySelector('iframe');
                if (iframe.contentWindow && iframe.contentWindow.triggerBehavior) {
                    addToStatus('3D场景加载完成，可以开始测试');
                    clearInterval(checkInterval);
                }
            }, 1000);
        });
    </script>
</body>
</html>
