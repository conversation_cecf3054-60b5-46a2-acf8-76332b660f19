<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功夫狗3D - Three.js项目</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(135deg, #87CEEB, #98FB98);
            font-family: 'Arial', sans-serif;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #canvas-container {
            width: 100%;
            height: 100%;
        }
        
        /* 对话气泡样式 */
        #speech-bubble {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid #333;
            border-radius: 20px;
            padding: 15px 25px;
            max-width: 400px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            display: none;
            z-index: 100;
        }
        
        #speech-bubble::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 15px solid #333;
        }
        
        #speech-bubble::before {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 12px solid transparent;
            border-right: 12px solid transparent;
            border-top: 12px solid rgba(255, 255, 255, 0.95);
        }
        


        /* 帮助面板样式 */
        #help-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            color: white;
            z-index: 100;
            max-width: 300px;
            font-size: 12px;
        }

        #help-panel h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        #help-panel ul {
            margin: 5px 0;
            padding-left: 15px;
        }

        #help-panel li {
            margin: 3px 0;
        }
        


        /* 状态面板样式 */
        #status-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 280px;
            background: rgba(0, 0, 0, 0.85);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-family: 'Arial', sans-serif;
            z-index: 150;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        #status-panel h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            text-align: center;
            font-size: 18px;
            border-bottom: 1px solid #4CAF50;
            padding-bottom: 8px;
        }

        .status-section {
            margin-bottom: 15px;
        }

        .status-section h4 {
            margin: 0 0 8px 0;
            color: #FFD700;
            font-size: 14px;
        }

        .health-bar {
            background: #333;
            border-radius: 10px;
            height: 20px;
            margin: 5px 0;
            overflow: hidden;
            border: 1px solid #666;
        }

        .health-fill {
            background: linear-gradient(90deg, #ff4444, #ff6666);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 9px;
        }

        .emotion-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 12px;
        }

        .emotion-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            border-left: 3px solid;
        }

        .emotion-item.happiness { border-left-color: #FFD700; }
        .emotion-item.energy { border-left-color: #FF6B35; }
        .emotion-item.alertness { border-left-color: #4ECDC4; }
        .emotion-item.friendliness { border-left-color: #45B7D1; }
        .emotion-item.anger { border-left-color: #FF4444; }
        .emotion-item.fear { border-left-color: #9B59B6; }
        .emotion-item.snark { border-left-color: #E67E22; }
        .emotion-item.playfulness { border-left-color: #2ECC71; }

        .emotion-value {
            font-weight: bold;
            color: #FFD700;
        }

        .status-info {
            font-size: 11px;
            color: #ccc;
            text-align: center;
            margin-top: 10px;
            padding-top: 8px;
            border-top: 1px solid #333;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 24px;
            font-weight: bold;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">正在加载功夫狗...</div>
        <div id="canvas-container"></div>
        
        <!-- 对话气泡 -->
        <div id="speech-bubble"></div>

        <!-- 帮助面板 -->
        <div id="help-panel">
            <h4>🎮 操作说明</h4>
            <ul>
                <li><strong>键盘控制：</strong></li>
                <li>1-4: 基础动作 (站立/行走/坐下/跳跃)</li>
                <li>Q/W/E/R: 功夫动作 (出拳/踢腿/翻滚/抱拳)</li>
                <li>空格: 打招呼</li>
                <li>回车: 随机说话</li>
                <li>Ctrl+K: 组合技</li>

            </ul>
            <ul>
                <li><strong>鼠标控制：</strong></li>
                <li>点击功夫狗不同部位触发动作</li>
                <li>点击功夫狗进行交互或攻击</li>
                <li>拖拽旋转视角</li>
                <li>滚轮缩放</li>
            </ul>
        </div>



        <!-- 状态面板 -->
        <div id="status-panel">
            <h3>🐕 功夫狗状态</h3>

            <!-- 生命值部分 -->
            <div class="status-section">
                <h4>❤️ 生命值</h4>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <span id="health-text">20/20</span>
                    <span id="health-status" style="color: #2ECC71;">正常</span>
                </div>
                <div class="health-bar">
                    <div class="health-fill" id="health-fill" style="width: 100%;"></div>
                </div>
            </div>

            <!-- 情绪状态部分 -->
            <div class="status-section">
                <h4>😊 情绪状态</h4>
                <div class="emotion-grid">
                    <div class="emotion-item happiness">
                        <span>😊 快乐</span>
                        <span class="emotion-value" id="emotion-happiness">0.70</span>
                    </div>
                    <div class="emotion-item energy">
                        <span>⚡ 精力</span>
                        <span class="emotion-value" id="emotion-energy">0.80</span>
                    </div>
                    <div class="emotion-item alertness">
                        <span>👁️ 警觉</span>
                        <span class="emotion-value" id="emotion-alertness">0.60</span>
                    </div>
                    <div class="emotion-item friendliness">
                        <span>🤝 友好</span>
                        <span class="emotion-value" id="emotion-friendliness">0.90</span>
                    </div>
                    <div class="emotion-item anger">
                        <span>😡 愤怒</span>
                        <span class="emotion-value" id="emotion-anger">0.10</span>
                    </div>
                    <div class="emotion-item fear">
                        <span>😨 恐惧</span>
                        <span class="emotion-value" id="emotion-fear">0.20</span>
                    </div>
                    <div class="emotion-item snark">
                        <span>😏 贱气</span>
                        <span class="emotion-value" id="emotion-snark">0.30</span>
                    </div>
                    <div class="emotion-item playfulness">
                        <span>🎮 玩心</span>
                        <span class="emotion-value" id="emotion-playfulness">0.80</span>
                    </div>
                </div>
            </div>

            <!-- 主导情绪 -->
            <div class="status-section">
                <h4>🎭 主导情绪</h4>
                <div style="text-align: center; padding: 8px; background: rgba(255, 215, 0, 0.2); border-radius: 8px; border: 1px solid #FFD700;">
                    <span id="dominant-emotion" style="color: #FFD700; font-weight: bold; font-size: 14px;">友好</span>
                </div>
            </div>

            <div class="status-info">
                实时更新 • 点击功夫狗查看详情
            </div>
        </div>
    </div>

    <!-- AI对话界面 -->
    <div id="ai-chat-panel" style="
        position: fixed;
        top: 20px;
        right: 320px;
        width: 300px;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 10px;
        padding: 15px;
        color: white;
        font-family: Arial, sans-serif;
        display: none;
        z-index: 1000;
    ">
        <h3 style="margin: 0 0 10px 0; color: #4CAF50;">🤖 与功夫狗对话</h3>
        <div id="ai-chat-history" style="
            height: 200px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            font-size: 12px;
        "></div>
        <div style="display: flex; gap: 5px;">
            <input type="text" id="ai-chat-input" placeholder="输入消息..." style="
                flex: 1;
                padding: 8px;
                border: none;
                border-radius: 5px;
                background: rgba(255, 255, 255, 0.9);
                color: black;
            ">
            <button onclick="sendAIMessage()" style="
                padding: 8px 12px;
                border: none;
                border-radius: 5px;
                background: #4CAF50;
                color: white;
                cursor: pointer;
            ">发送</button>
        </div>
        <div style="margin-top: 10px; font-size: 10px; color: #ccc;">
            <button onclick="clearAIChatHistory()" style="
                padding: 4px 8px;
                border: none;
                border-radius: 3px;
                background: #ff6b6b;
                color: white;
                cursor: pointer;
                font-size: 10px;
            ">清空历史</button>
            <span style="margin-left: 10px;">按Enter发送</span>
        </div>
    </div>
    
    <script type="module" src="/src/main.js"></script>
</body>
</html>
