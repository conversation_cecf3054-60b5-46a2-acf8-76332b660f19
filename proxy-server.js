/**
 * 阿里云API代理服务器
 * 解决浏览器CORS跨域问题
 */

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3004;

// 阿里云API配置
const DASHSCOPE_API_KEY = 'sk-a42b37159a09413ebeb7dcb7c3dade9d';
const DASHSCOPE_BASE_URL = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

// 中间件
app.use(cors()); // 允许跨域
app.use(express.json()); // 解析JSON请求体

// 代理路由
app.post('/api/chat', async (req, res) => {
    try {
        console.log('🤖 收到AI请求:', req.body);

        // 动态导入node-fetch
        const { default: fetch } = await import('node-fetch');

        // 转发请求到阿里云API
        const response = await fetch(DASHSCOPE_BASE_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${DASHSCOPE_API_KEY}`,
                'X-DashScope-SSE': 'disable'
            },
            body: JSON.stringify(req.body)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ 阿里云API错误:', response.status, errorText);
            return res.status(response.status).json({
                error: `API请求失败: ${response.status} - ${errorText}`
            });
        }

        const data = await response.json();
        console.log('✅ 阿里云API响应成功');
        
        res.json(data);

    } catch (error) {
        console.error('❌ 代理服务器错误:', error);
        res.status(500).json({
            error: '代理服务器内部错误',
            details: error.message
        });
    }
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({ status: 'ok', message: '代理服务器运行正常' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 阿里云API代理服务器启动成功`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🔗 代理地址: http://localhost:${PORT}/api/chat`);
    console.log(`💡 健康检查: http://localhost:${PORT}/health`);
});
