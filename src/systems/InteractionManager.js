/**
 * 交互管理器
 * 负责处理用户与功夫狗的各种交互，包括点击、键盘输入等
 * 符合规则文件要求：支持用户触发特定动作及对话
 */

import * as THREE from 'three';

export class InteractionManager {
    constructor(kungFuDog, animationController, speechSystem, sceneManager) {
        this.kungFuDog = kungFuDog;
        this.animationController = animationController;
        this.speechSystem = speechSystem;
        this.sceneManager = sceneManager;

        // 特效系统（将在主应用中设置）
        this.particleSystem = null;
        this.audioSystem = null;
        this.battleSystem = null;
        this.aiSystem = null;
        this.behaviorSystem = null;
        
        // 交互状态
        this.isInteractionEnabled = true;
        this.lastInteractionTime = 0;
        this.interactionCooldown = 150; // 交互冷却时间（大幅减少以支持连续快速点击）

        // 战斗状态跟踪（用于决定交互类型）
        this.lastBattleTime = 0;
        this.battleCooldownPeriod = 30000; // 30秒内保持战斗状态

        // 网格部位映射 - 可以根据实际模型调整
        this.meshBodyParts = {
            'Cube001_1': 'head',        // 头部
            'Cube001_2': 'body',        // 身体
            'Cube001_3': 'legs',        // 腿部
            'Cube001_4': 'feet',        // 脚部
            // 可以根据实际模型添加更多映射
        };

        // 键盘映射
        this.keyMappings = {
            '1': 'stand',
            '2': 'walk', 
            '3': 'sit',
            '4': 'jump',
            'q': 'punch',
            'w': 'kick',
            'e': 'roll',
            'r': 'bow',
            ' ': 'greet', // 空格键打招呼
            'Enter': 'randomSpeak'
        };

        this.init();
    }
    
    init() {
        // console.log('🎮 初始化交互管理器...');
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // console.log('✅ 交互管理器初始化完成');
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        
        // 鼠标点击功夫狗
        if (this.sceneManager.renderer) {
            this.sceneManager.renderer.domElement.addEventListener('click', this.handleCanvasClick.bind(this));
        }
        
        // 窗口焦点事件
        window.addEventListener('focus', this.handleWindowFocus.bind(this));
        window.addEventListener('blur', this.handleWindowBlur.bind(this));
        
        console.log('🎯 交互事件监听器设置完成');
    }
    
    handleKeyDown(event) {
        if (!this.isInteractionEnabled) return;
        
        const key = event.key;
        const action = this.keyMappings[key];
        
        if (action) {
            event.preventDefault();
            this.triggerAction(action);
        }
        
        // 特殊组合键 - 现在触发 BehaviorSystem 的功夫表演
        if (event.ctrlKey && key === 'k') {
            event.preventDefault();
            if (this.behaviorSystem) {
                this.behaviorSystem.performKungfuDemo();
            }
        }
    }
    
    handleCanvasClick(event) {
        if (!this.isInteractionEnabled) return;
        
        // 获取鼠标点击位置
        const rect = this.sceneManager.renderer.domElement.getBoundingClientRect();
        const mouse = {
            x: ((event.clientX - rect.left) / rect.width) * 2 - 1,
            y: -((event.clientY - rect.top) / rect.height) * 2 + 1
        };
        
        // 射线检测
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.sceneManager.camera);

        // 可视化射线（如果启用）
        if (this.kungFuDog && this.kungFuDog.getRayVisualizationStatus().enabled) {
            this.kungFuDog.visualizeRay(raycaster);
        }

        // 检测是否点击了功夫狗
        if (this.kungFuDog.model) {
            let intersection = null;

            // 优先检测 Armature 碰撞体（更容易点击）
            const armatureCollisions = this.kungFuDog.getArmatureCollision();
            if (armatureCollisions && armatureCollisions.length > 0) {
                const armatureIntersects = raycaster.intersectObjects(armatureCollisions, false);
                if (armatureIntersects.length > 0) {
                    intersection = armatureIntersects[0];
                    const boxIndex = intersection.object.userData.boxIndex;
                    console.log(`🎯 通过 Armature 碰撞体检测到点击！(碰撞盒 ${boxIndex + 1})`);
                }
            }

            // 如果 Armature 碰撞体没有检测到，尝试检测整个模型
            if (!intersection) {
                const allIntersects = raycaster.intersectObject(this.kungFuDog.model, true);
                if (allIntersects.length > 0) {
                    intersection = allIntersects[0];
                    console.log('🎯 通过模型检测到点击！');
                }
            }

            if (intersection) {
                console.log('🐕 点击了功夫狗！');

                // 可视化命中点（如果启用射线可视化）
                if (this.kungFuDog.getRayVisualizationStatus().enabled) {
                    this.kungFuDog.visualizeHitPoint(intersection.point);
                }

                this.handleDogClick(intersection);
            }
        }
    }
    
    handleDogClick(intersection) {
        const clickedObject = intersection.object;
        const point = intersection.point;
        const meshName = clickedObject.name;

        console.log(`🎯 点击位置: (${point.x.toFixed(2)}, ${point.y.toFixed(2)}, ${point.z.toFixed(2)})`);
        console.log(`🎯 点击网格: ${meshName}`);

        // 基于网格名称的精确部位检测
        const bodyPart = this.identifyBodyPart(meshName, point, clickedObject);
        console.log(`🎯 识别部位: ${bodyPart}`);

        // 根据功夫狗状态决定交互类型
        if (this.shouldTriggerBattleInteraction()) {
            console.log('⚔️ 触发战斗交互');
            if (this.battleSystem) {
                this.battleSystem.handleAttack(this.kungFuDog);
                this.lastBattleTime = Date.now(); // 更新最后战斗时间
            }
        } else {
            console.log('🤝 触发友好交互');
            // 根据识别的部位触发相应行为（通过 BehaviorSystem）
            this.handleBodyPartInteraction(bodyPart);
        }
    }

    /**
     * 识别点击的身体部位
     * @param {string} meshName - 网格名称
     * @param {Vector3} point - 点击的3D坐标
     * @param {Object} clickedObject - 被点击的对象
     * @returns {string} 身体部位名称
     */
    identifyBodyPart(meshName, point, clickedObject = null) {
        // 优先检查是否为 Armature 碰撞体
        if (clickedObject && clickedObject.userData && clickedObject.userData.isArmatureCollision) {
            const boxIndex = clickedObject.userData.boxIndex;
            if (boxIndex === 0) {
                console.log(`✅ 通过 Armature 碰撞体识别: ${meshName} -> body (下方碰撞盒)`);
                return 'body';
            } else if (boxIndex === 1) {
                console.log(`✅ 通过 Armature 碰撞体识别: ${meshName} -> head (上方碰撞盒)`);
                return 'head';
            }
        }

        // 检查是否为旧的碰撞盒
        if (clickedObject && clickedObject.userData && clickedObject.userData.isCollisionBox) {
            const bodyPart = clickedObject.userData.bodyPart;
            console.log(`✅ 通过碰撞盒识别: ${meshName} -> ${bodyPart}`);
            return bodyPart;
        }

        // 检查网格名称映射
        if (this.meshBodyParts[meshName]) {
            console.log(`✅ 通过网格名称识别: ${meshName} -> ${this.meshBodyParts[meshName]}`);
            return this.meshBodyParts[meshName];
        }

        // 如果网格名称不在映射中，使用Y坐标作为后备方案
        console.log(`⚠️ 网格 "${meshName}" 未在映射中，使用坐标判断`);

        if (point.y > 1.8) {
            return 'head';
        } else if (point.y > 0.8) {
            return 'body';
        } else if (point.y > 0.2) {
            return 'legs';
        } else {
            return 'feet';
        }
    }

    /**
     * 判断是否应该触发战斗交互
     * @returns {boolean} 是否触发战斗交互
     */
    shouldTriggerBattleInteraction() {
        if (!this.kungFuDog || !this.kungFuDog.healthSystem) {
            return false;
        }

        const now = Date.now();
        const health = this.kungFuDog.healthSystem.health;
        const maxHealth = this.kungFuDog.healthSystem.maxHealth;

        // 如果生命值不满，总是触发战斗交互
        if (health < maxHealth) {
            return true;
        }

        // 如果最近有战斗交互（30秒内），继续战斗状态
        if (now - this.lastBattleTime < this.battleCooldownPeriod) {
            return true;
        }

        // 生命值满且无近期战斗时，50%概率触发战斗交互，50%友好交互
        // 这样用户可以主动发起攻击
        const battleChance = 0.5;
        const random = Math.random();
        console.log(`🎲 战斗概率判定: ${random.toFixed(3)} < ${battleChance} = ${random < battleChance ? '战斗' : '友好'}`);

        return random < battleChance;
    }

    /**
     * 处理身体部位交互（通过 BehaviorSystem）
     * @param {string} bodyPart - 身体部位名称
     */
    handleBodyPartInteraction(bodyPart) {
        if (!this.behaviorSystem) {
            console.warn('⚠️ BehaviorSystem 未初始化，无法处理身体部位交互');
            return;
        }

        // 根据身体部位触发相应的行为
        switch (bodyPart) {
            case 'head':
                console.log('🐕 点击了头部 - 触发问候行为');
                this.behaviorSystem.triggerBehavior('greeting');
                break;

            case 'body':
                console.log('� 点击了身体 - 触发炫技行为');
                this.behaviorSystem.triggerBehavior('showOff');
                break;

            case 'legs':
                console.log('🐕 点击了腿部 - 触发活跃行为');
                this.behaviorSystem.triggerBehavior('energetic');
                break;

            case 'feet':
                console.log('🐕 点击了脚部 - 触发思考行为');
                this.behaviorSystem.triggerBehavior('contemplation');
                break;

            default:
                console.log(`🐕 未知部位: ${bodyPart} - 触发待机行为`);
                this.behaviorSystem.triggerBehavior('idle');
        }
    }



    handleWindowFocus() {
        console.log('👀 窗口获得焦点');
        this.speechSystem.speak('欢迎回来！', this.kungFuDog);
    }
    
    handleWindowBlur() {
        console.log('😴 窗口失去焦点');
        // 可以在这里暂停一些动画或降低帧率
    }
    
    triggerAction(actionName) {
        const now = Date.now();
        
        // 检查冷却时间
        if (now - this.lastInteractionTime < this.interactionCooldown) {
            console.log('⏰ 交互冷却中...');
            return;
        }
        
        this.lastInteractionTime = now;

        // 通知行为系统有用户交互
        if (this.behaviorSystem) {
            this.behaviorSystem.recordInteraction();
        }

        console.log(`🎬 触发交互动作: ${actionName}`);
        
        // 处理特殊动作
        switch (actionName) {
            case 'greet':
                this.speechSystem.greet(this.kungFuDog);
                this.animationController.playAction('bow');
                break;
                
            case 'randomSpeak':
                this.speechSystem.speakRandom(this.kungFuDog);
                break;
                
            case 'kungfuDemo':
                // 使用 BehaviorSystem 的功夫表演
                if (this.behaviorSystem) {
                    this.behaviorSystem.performKungfuDemo();
                } else {
                    console.warn('⚠️ BehaviorSystem 未初始化，无法执行功夫表演');
                }
                break;



            default:
                // 普通动作 - 现在优先使用 BehaviorSystem
                if (this.behaviorSystem) {
                    // 将简单动作转换为对应的行为
                    const actionToBehaviorMap = {
                        'punch': 'showOff',
                        'kick': 'showOff',
                        'jump': 'energetic',
                        'roll': 'energetic',
                        'bow': 'greeting',
                        'walk': 'energetic',
                        'sit': 'contemplation',
                        'stand': 'idle'
                    };

                    const behaviorName = actionToBehaviorMap[actionName] || 'idle';
                    console.log(`🎭 将动作 ${actionName} 转换为行为 ${behaviorName}`);
                    this.behaviorSystem.triggerBehavior(behaviorName);
                } else {
                    console.warn('⚠️ BehaviorSystem 未初始化，无法执行动作');
                }
        }
    }
    


    enableInteraction() {
        this.isInteractionEnabled = true;
        console.log('✅ 交互已启用');
    }
    
    disableInteraction() {
        this.isInteractionEnabled = false;
        console.log('❌ 交互已禁用');
    }
    
    setInteractionCooldown(ms) {
        this.interactionCooldown = ms;
    }
    
    addKeyMapping(key, action) {
        this.keyMappings[key] = action;
    }
    
    removeKeyMapping(key) {
        delete this.keyMappings[key];
    }

    setAudioSystem(audioSystem) {
        this.audioSystem = audioSystem;
    }

    setParticleSystem(particleSystem) {
        this.particleSystem = particleSystem;
    }

    setBattleSystem(battleSystem) {
        this.battleSystem = battleSystem;
    }

    setAISystem(aiSystem) {
        this.aiSystem = aiSystem;
    }

    setBehaviorSystem(behaviorSystem) {
        this.behaviorSystem = behaviorSystem;
        // console.log('🎭 行为系统已设置到交互管理器');
    }



    dispose() {
        // 移除事件监听器
        document.removeEventListener('keydown', this.handleKeyDown.bind(this));
        
        if (this.sceneManager.renderer) {
            this.sceneManager.renderer.domElement.removeEventListener('click', this.handleCanvasClick.bind(this));
        }
        
        window.removeEventListener('focus', this.handleWindowFocus.bind(this));
        window.removeEventListener('blur', this.handleWindowBlur.bind(this));
        
        console.log('🗑️ 交互管理器已清理');
    }
}
