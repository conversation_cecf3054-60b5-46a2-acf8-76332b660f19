/**
 * 台词管理系统
 * 负责管理角色的所有台词内容，支持分类、随机选择、国际化等功能
 */
class SpeechManager {
    constructor() {
        this.speeches = null;
        this.currentLanguage = 'zh-CN'; // 默认中文
        this.speechHistory = new Map(); // 记录每个类别的台词历史，避免重复
        this.maxHistoryLength = 3; // 最大历史记录长度
        
        // console.log('🗣️ SpeechManager 初始化完成');
    }

    /**
     * 加载台词数据
     * @param {string} language - 语言代码 (zh-CN, en-US, etc.)
     */
    async loadSpeeches(language = 'zh-CN') {
        try {
            this.currentLanguage = language;
            
            // 根据语言加载对应的台词文件
            const speechFile = language === 'zh-CN' ? 
                '/src/data/speeches.json' : 
                `/src/data/speeches_${language}.json`;
            
            const response = await fetch(speechFile);
            if (!response.ok) {
                throw new Error(`Failed to load speeches: ${response.status}`);
            }
            
            this.speeches = await response.json();
            // console.log(`🗣️ 台词数据加载完成 (${language})`);
            // console.log(`📊 台词统计:`, this.getSpeechStatistics());
            
            return true;
        } catch (error) {
            console.error('❌ 台词数据加载失败:', error);
            // 使用默认台词作为后备
            this.speeches = this.getDefaultSpeeches();
            return false;
        }
    }

    /**
     * 获取指定类别的随机台词
     * @param {string} category - 主类别 (behaviors, combat, emotions, etc.)
     * @param {string} subcategory - 子类别 (idle, attack, happy, etc.)
     * @param {boolean} avoidRepeat - 是否避免重复 (默认: true)
     * @returns {string} 台词内容
     */
    getSpeech(category, subcategory, avoidRepeat = true) {
        if (!this.speeches) {
            console.warn('⚠️ 台词数据未加载，使用默认台词');
            return this.getDefaultSpeech(category, subcategory);
        }

        const categoryData = this.speeches[category];
        if (!categoryData) {
            console.warn(`⚠️ 未找到类别: ${category}`);
            return this.getDefaultSpeech(category, subcategory);
        }

        const speechList = categoryData[subcategory];
        if (!speechList || !Array.isArray(speechList) || speechList.length === 0) {
            console.warn(`⚠️ 未找到子类别: ${category}.${subcategory}`);
            return this.getDefaultSpeech(category, subcategory);
        }

        // 如果只有一条台词，直接返回
        if (speechList.length === 1) {
            return speechList[0];
        }

        // 避免重复的逻辑
        if (avoidRepeat) {
            return this.getAvoidRepeatSpeech(category, subcategory, speechList);
        } else {
            return speechList[Math.floor(Math.random() * speechList.length)];
        }
    }

    /**
     * 获取避免重复的台词
     * @param {string} category - 主类别
     * @param {string} subcategory - 子类别
     * @param {Array} speechList - 台词列表
     * @returns {string} 台词内容
     */
    getAvoidRepeatSpeech(category, subcategory, speechList) {
        const key = `${category}.${subcategory}`;
        const history = this.speechHistory.get(key) || [];
        
        // 过滤掉最近使用过的台词
        let availableSpeeches = speechList.filter(speech => !history.includes(speech));
        
        // 如果所有台词都用过了，重置历史记录
        if (availableSpeeches.length === 0) {
            availableSpeeches = speechList;
            this.speechHistory.set(key, []);
        }
        
        // 随机选择一条台词
        const selectedSpeech = availableSpeeches[Math.floor(Math.random() * availableSpeeches.length)];
        
        // 更新历史记录
        const newHistory = [selectedSpeech, ...history].slice(0, this.maxHistoryLength);
        this.speechHistory.set(key, newHistory);
        
        return selectedSpeech;
    }

    /**
     * 获取行为相关的台词
     * @param {string} behaviorType - 行为类型
     * @returns {string} 台词内容
     */
    getBehaviorSpeech(behaviorType) {
        return this.getSpeech('behaviors', behaviorType);
    }

    /**
     * 获取战斗相关的台词
     * @param {string} combatType - 战斗类型
     * @returns {string} 台词内容
     */
    getCombatSpeech(combatType) {
        return this.getSpeech('combat', combatType);
    }

    /**
     * 获取情绪相关的台词
     * @param {string} emotionType - 情绪类型
     * @returns {string} 台词内容
     */
    getEmotionSpeech(emotionType) {
        return this.getSpeech('emotions', emotionType);
    }

    /**
     * 获取交互相关的台词
     * @param {string} interactionType - 交互类型
     * @returns {string} 台词内容
     */
    getInteractionSpeech(interactionType) {
        return this.getSpeech('interactions', interactionType);
    }

    /**
     * 获取系统相关的台词
     * @param {string} systemType - 系统类型
     * @returns {string} 台词内容
     */
    getSystemSpeech(systemType) {
        return this.getSpeech('system', systemType);
    }

    /**
     * 根据情绪状态获取合适的台词
     * @param {Object} emotionalState - 情绪状态对象
     * @returns {string} 台词内容
     */
    getSpeechByEmotion(emotionalState) {
        // 根据主导情绪选择台词类型
        const dominantEmotion = this.getDominantEmotion(emotionalState);
        
        switch (dominantEmotion) {
            case 'happiness':
                return this.getEmotionSpeech('happy');
            case 'anger':
                return this.getEmotionSpeech('angry');
            case 'fear':
                return this.getEmotionSpeech('sad');
            case 'playfulness':
                return this.getEmotionSpeech('excited');
            case 'alertness':
                return this.getEmotionSpeech('calm');
            default:
                return this.getBehaviorSpeech('idle');
        }
    }

    /**
     * 获取主导情绪
     * @param {Object} emotionalState - 情绪状态对象
     * @returns {string} 主导情绪名称
     */
    getDominantEmotion(emotionalState) {
        let maxValue = 0;
        let dominantEmotion = 'happiness';

        for (const [emotion, value] of Object.entries(emotionalState)) {
            if (value > maxValue) {
                maxValue = value;
                dominantEmotion = emotion;
            }
        }

        return dominantEmotion;
    }

    /**
     * 获取台词统计信息
     * @returns {Object} 统计信息
     */
    getSpeechStatistics() {
        if (!this.speeches) return null;

        const stats = {};
        let totalSpeeches = 0;

        for (const [category, categoryData] of Object.entries(this.speeches)) {
            stats[category] = {};
            let categoryTotal = 0;

            for (const [subcategory, speechList] of Object.entries(categoryData)) {
                if (Array.isArray(speechList)) {
                    stats[category][subcategory] = speechList.length;
                    categoryTotal += speechList.length;
                    totalSpeeches += speechList.length;
                }
            }

            stats[category].total = categoryTotal;
        }

        stats.grandTotal = totalSpeeches;
        return stats;
    }

    /**
     * 添加自定义台词
     * @param {string} category - 主类别
     * @param {string} subcategory - 子类别
     * @param {string|Array} speeches - 台词内容（字符串或数组）
     */
    addCustomSpeech(category, subcategory, speeches) {
        if (!this.speeches) {
            this.speeches = {};
        }

        if (!this.speeches[category]) {
            this.speeches[category] = {};
        }

        if (!this.speeches[category][subcategory]) {
            this.speeches[category][subcategory] = [];
        }

        const speechArray = Array.isArray(speeches) ? speeches : [speeches];
        this.speeches[category][subcategory].push(...speechArray);

        console.log(`✅ 添加自定义台词: ${category}.${subcategory} (+${speechArray.length})`);
    }

    /**
     * 获取默认台词（后备方案）
     * @param {string} category - 主类别
     * @param {string} subcategory - 子类别
     * @returns {string} 默认台词
     */
    getDefaultSpeech(category, subcategory) {
        const defaultSpeeches = {
            'behaviors.idle': '我在这里等着呢~',
            'behaviors.greeting': '很高兴见到你！',
            'behaviors.showOff': '看我的功夫！',
            'combat.attack': '接招！',
            'combat.hit': '好痛！',
            'emotions.happy': '心情真好！',
            'interactions.greeting_first': '大家好，我是功夫狗！'
        };

        const key = `${category}.${subcategory}`;
        return defaultSpeeches[key] || '...';
    }

    /**
     * 获取默认台词数据结构
     * @returns {Object} 默认台词数据
     */
    getDefaultSpeeches() {
        return {
            behaviors: {
                idle: ['我在这里等着呢~', '有什么需要帮助的吗？'],
                greeting: ['很高兴见到你！', '欢迎来到功夫世界！'],
                showOff: ['看我的功夫！', '这就是真正的武术！']
            },
            combat: {
                attack: ['接招！', '看我的厉害！'],
                hit: ['好痛！', '这一击很重！']
            },
            emotions: {
                happy: ['心情真好！', '今天是美好的一天！']
            },
            interactions: {
                greeting_first: ['大家好，我是功夫狗！准备好看我的功夫了吗？']
            }
        };
    }

    /**
     * 清除台词历史记录
     * @param {string} category - 可选，指定类别
     * @param {string} subcategory - 可选，指定子类别
     */
    clearHistory(category = null, subcategory = null) {
        if (category && subcategory) {
            const key = `${category}.${subcategory}`;
            this.speechHistory.delete(key);
            console.log(`🗑️ 清除台词历史: ${key}`);
        } else if (category) {
            for (const key of this.speechHistory.keys()) {
                if (key.startsWith(`${category}.`)) {
                    this.speechHistory.delete(key);
                }
            }
            console.log(`🗑️ 清除台词历史: ${category}.*`);
        } else {
            this.speechHistory.clear();
            console.log('🗑️ 清除所有台词历史');
        }
    }

    /**
     * 设置语言
     * @param {string} language - 语言代码
     */
    async setLanguage(language) {
        if (language !== this.currentLanguage) {
            await this.loadSpeeches(language);
            this.clearHistory(); // 切换语言时清除历史记录
        }
    }
}

export default SpeechManager;
