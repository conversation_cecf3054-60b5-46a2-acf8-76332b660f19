/**
 * 功夫狗战斗系统
 * 处理攻击交互、生命值管理、战斗状态和相关动画音效
 */

export class BattleSystem {
    constructor(animationController, audioSystem, speechSystem, particleSystem = null) {
        // 核心系统引用
        this.animationController = animationController;
        this.audioSystem = audioSystem;
        this.speechSystem = speechSystem;
        this.particleSystem = particleSystem;
        
        // 战斗数据现在由 KungFuDog 角色类管理
        this.isBattleMode = false;
        
        // 战斗配置
        this.config = {
            // 基础概率（情绪中性时的默认值）
            baseBlockChance: 0.4,        // 40% 基础格挡概率
            baseCounterAttackChance: 0.5, // 50% 基础反击概率

            // 情绪影响系数
            emotionModifiers: {
                // 愤怒对战斗的影响
                anger: {
                    blockMultiplier: 0.6,    // 愤怒降低格挡专注度 (40% → 24%)
                    counterMultiplier: 1.8   // 愤怒大幅提高反击欲望 (50% → 90%)
                },
                // 恐惧对战斗的影响
                fear: {
                    blockMultiplier: 1.5,    // 恐惧提高防御意识 (40% → 60%)
                    counterMultiplier: 0.4   // 恐惧降低反击意愿 (50% → 20%)
                },
                // 警觉性对战斗的影响
                alertness: {
                    blockMultiplier: 1.75,   // 警觉性大幅提高格挡 (40% → 70%)
                    counterMultiplier: 1.0   // 警觉性对反击影响中性
                },
                // 贱气对战斗的影响
                snark: {
                    blockMultiplier: 1.0,    // 贱气对格挡影响中性
                    counterMultiplier: 1.5   // 贱气增加反击冲动 (50% → 75%)
                },
                // 精力对战斗的影响
                energy: {
                    blockMultiplier: 1.1,    // 精力略微提高格挡
                    counterMultiplier: 1.2   // 精力提高反击能力
                },
                // 友好度对战斗的影响
                friendliness: {
                    blockMultiplier: 1.0,    // 友好度对格挡影响中性
                    counterMultiplier: 0.8   // 友好度降低反击意愿
                }
            },

            battleCooldown: 200,     // 战斗交互冷却时间
            animationDuration: 1000, // 动画持续时间
            counterAttackDelay: 600  // 反击延迟时间
        };
        
        // 动画映射
        this.animations = {
            block: 'Block',
            hurt: 'Hurt',
            down: 'Left leg hurt',
            counterAttack: 'kick'  // 反击动画
        };
        
        // 音效映射
        this.sounds = {
            block: 'block-sound',
            hurt: 'hurt-sound',
            down: 'down-sound',
            counterAttack: 'kick-sound'  // 反击音效
        };
        
        // 对话内容
        this.dialogues = {
            block: [
                "挡住了，汪！",
                "想偷袭我？没门！",
                "我的防御可是很强的！",
                "哈哈，没打中！"
            ],
            hurt: [
                "哎呀...",
                "好痛啊！",
                "你这家伙...",
                "汪呜..."
            ],
            down: [
                "我输了...",
                "不行了，需要休息一下...",
                "你赢了，汪...",
                "功夫还需要继续练习..."
            ],
            counterAttack: [
                "反击！看我的功夫！",
                "格挡成功，该我了！",
                "接我一脚！",
                "反击时间到！汪！"
            ]
        };
        
        // 战斗状态
        this.lastBattleTime = 0;
        this.battleStats = {
            totalAttacks: 0,
            blockedAttacks: 0,
            successfulHits: 0,
            counterAttacks: 0  // 反击次数统计
        };

        // 连击系统
        this.comboSystem = {
            count: 0,
            lastClickTime: 0,
            comboTimeout: 2000, // 2秒内连续点击才算连击
            maxCombo: 50        // 最大连击数
        };

        // 语音控制系统
        this.speechControl = {
            lastSpeechTime: 0,
            speechCooldown: 4000,    // 4秒语音冷却时间
            comboSpeechInterval: 10, // 每10次连击说一次话
            speechProbability: 0.2   // 常规战斗动作20%概率说话
        };

        // 概率缓存（用于减少日志输出频率）
        this.lastBlockChance = null;
        this.lastCounterChance = null;
        
        // console.log('⚔️ 战斗系统初始化完成');
        this.createBattleUI();
    }

    /**
     * 根据情绪状态计算动态格挡概率
     * @param {Object} character - 功夫狗角色对象
     * @returns {number} 动态格挡概率 (0-1)
     */
    calculateBlockChance(character) {
        if (!character || !character.getEmotionalState) {
            return this.config.baseBlockChance;
        }

        const emotions = character.getEmotionalState();
        let blockChance = this.config.baseBlockChance;
        let totalModifier = 1.0;

        // 计算各种情绪的影响
        const modifiers = this.config.emotionModifiers;

        // 愤怒的影响（降低格挡专注度）
        if (emotions.anger > 0.1) {
            const angerEffect = emotions.anger * (modifiers.anger.blockMultiplier - 1);
            totalModifier += angerEffect;
        }

        // 恐惧的影响（提高防御意识）
        if (emotions.fear > 0.1) {
            const fearEffect = emotions.fear * (modifiers.fear.blockMultiplier - 1);
            totalModifier += fearEffect;
        }

        // 警觉性的影响（大幅提高格挡）
        if (emotions.alertness > 0.1) {
            const alertnessEffect = emotions.alertness * (modifiers.alertness.blockMultiplier - 1);
            totalModifier += alertnessEffect;
        }

        // 精力的影响（略微提高格挡）
        if (emotions.energy > 0.1) {
            const energyEffect = emotions.energy * (modifiers.energy.blockMultiplier - 1);
            totalModifier += energyEffect;
        }

        // 应用修正系数并限制在合理范围内
        blockChance *= totalModifier;
        blockChance = Math.max(0.1, Math.min(0.9, blockChance)); // 限制在10%-90%之间

        // 只在概率发生显著变化时输出日志（避免频繁输出）
        if (!this.lastBlockChance || Math.abs(blockChance - this.lastBlockChance) > 0.05) {
            console.log(`🛡️ 动态格挡概率: ${(blockChance * 100).toFixed(1)}% (基础: ${(this.config.baseBlockChance * 100).toFixed(1)}%, 修正: ${(totalModifier * 100).toFixed(1)}%)`);
            this.lastBlockChance = blockChance;
        }

        return blockChance;
    }

    /**
     * 根据情绪状态计算动态反击概率
     * @param {Object} character - 功夫狗角色对象
     * @returns {number} 动态反击概率 (0-1)
     */
    calculateCounterAttackChance(character) {
        if (!character || !character.getEmotionalState) {
            return this.config.baseCounterAttackChance;
        }

        const emotions = character.getEmotionalState();
        let counterChance = this.config.baseCounterAttackChance;
        let totalModifier = 1.0;

        // 计算各种情绪的影响
        const modifiers = this.config.emotionModifiers;

        // 愤怒的影响（大幅提高反击欲望）
        if (emotions.anger > 0.1) {
            const angerEffect = emotions.anger * (modifiers.anger.counterMultiplier - 1);
            totalModifier += angerEffect;
        }

        // 恐惧的影响（降低反击意愿）
        if (emotions.fear > 0.1) {
            const fearEffect = emotions.fear * (modifiers.fear.counterMultiplier - 1);
            totalModifier += fearEffect;
        }

        // 贱气的影响（增加反击冲动）
        if (emotions.snark > 0.1) {
            const snarkEffect = emotions.snark * (modifiers.snark.counterMultiplier - 1);
            totalModifier += snarkEffect;
        }

        // 精力的影响（提高反击能力）
        if (emotions.energy > 0.1) {
            const energyEffect = emotions.energy * (modifiers.energy.counterMultiplier - 1);
            totalModifier += energyEffect;
        }

        // 友好度的影响（降低反击意愿）
        if (emotions.friendliness > 0.1) {
            const friendlinessEffect = emotions.friendliness * (modifiers.friendliness.counterMultiplier - 1);
            totalModifier += friendlinessEffect;
        }

        // 应用修正系数并限制在合理范围内
        counterChance *= totalModifier;
        counterChance = Math.max(0.05, Math.min(0.95, counterChance)); // 限制在5%-95%之间

        // 只在概率发生显著变化时输出日志（避免频繁输出）
        if (!this.lastCounterChance || Math.abs(counterChance - this.lastCounterChance) > 0.05) {
            console.log(`⚡ 动态反击概率: ${(counterChance * 100).toFixed(1)}% (基础: ${(this.config.baseCounterAttackChance * 100).toFixed(1)}%, 修正: ${(totalModifier * 100).toFixed(1)}%)`);
            this.lastCounterChance = counterChance;
        }

        return counterChance;
    }
    
    /**
     * 处理攻击交互的主要方法
     * @param {Object} character - 功夫狗角色对象
     */
    handleAttack(character) {
        // 检查是否已经倒下
        if (character.isDownState && character.isDownState()) {
            console.log('💀 功夫狗已经倒下，无法继续战斗');
            this.speakWithControl('我已经倒下了，让我休息一下吧...', character, 'important', 0);
            return;
        }
        
        // 检查冷却时间
        const now = Date.now();
        if (now - this.lastBattleTime < this.config.battleCooldown) {
            // 即使在冷却期间也给用户一些反馈
            console.log('⏰ 战斗冷却中...');

            // 添加快速的视觉反馈
            if (this.particleSystem && character.model) {
                const position = character.model.position.clone();
                position.y += 1; // 在角色上方显示
                this.particleSystem.createClickFeedback(position);
            }

            // 播放轻微的音效反馈
            if (this.audioSystem && this.audioSystem.playSound) {
                this.audioSystem.playSound('punch'); // 使用现有的音效
            }
            return;
        }
        
        this.lastBattleTime = now;
        this.battleStats.totalAttacks++;

        // 保存角色引用用于连击语音
        this.lastCharacter = character;

        // 更新连击计数
        this.updateComboCount(now);

        console.log(`⚔️ 开始攻击交互流程... 连击: ${this.comboSystem.count}`);

        // 计算基于情绪的动态格挡概率
        const dynamicBlockChance = this.calculateBlockChance(character);

        // 随机判定结果
        const random = Math.random();
        console.log(`🎲 随机数: ${random.toFixed(3)}, 格挡阈值: ${dynamicBlockChance.toFixed(3)}`);

        if (random < dynamicBlockChance) {
            // 动态概率格挡
            this.performBlock(character);
        } else {
            // 动态概率受击
            this.performHurt(character);
        }

        // 更新UI（传递character参数以获取最新生命值）
        this.updateBattleUI(character);
    }
    
    /**
     * 执行格挡动作
     * @param {Object} character - 功夫狗角色对象
     */
    performBlock(character) {
        console.log('🛡️ 执行格挡动作');
        this.battleStats.blockedAttacks++;

        // 播放格挡动画（不循环，动画控制器会自动判断）
        this.animationController.playAction(this.animations.block);

        // 播放格挡音效
        this.audioSystem.playSound(this.sounds.block);

        // 触发防御情绪状态更新
        if (character.updateEmotionalState) {
            character.updateEmotionalState('defensive');
        }

        // 显示格挡对话（降低频率）
        const dialogue = this.getRandomDialogue('block');
        this.speakWithControl(dialogue, character, 'normal', 300);

        console.log('✅ 格挡成功！生命值未减少');

        // 判断是否触发反击
        this.checkCounterAttack(character);
    }

    /**
     * 检查并执行反击
     * @param {Object} character - 功夫狗角色对象
     */
    checkCounterAttack(character) {
        // 计算基于情绪的动态反击概率
        const dynamicCounterChance = this.calculateCounterAttackChance(character);

        // 随机判定是否反击
        const random = Math.random();
        console.log(`🎲 反击判定随机数: ${random.toFixed(3)}, 反击阈值: ${dynamicCounterChance.toFixed(3)}`);

        if (random < dynamicCounterChance) {
            console.log('⚡ 触发反击！');

            // 延迟执行反击，等待格挡动画播放
            setTimeout(() => {
                this.performCounterAttack(character);
            }, this.config.counterAttackDelay);
        } else {
            console.log('❌ 未触发反击');
        }
    }

    /**
     * 执行反击动作
     * @param {Object} character - 功夫狗角色对象
     */
    performCounterAttack(character) {
        console.log('⚡ 执行反击动作');
        this.battleStats.counterAttacks++;

        // 播放反击动画（kick）
        this.animationController.playAction(this.animations.counterAttack);

        // 播放反击音效
        this.audioSystem.playSound(this.sounds.counterAttack);

        // 触发攻击性情绪状态更新
        if (character.updateEmotionalState) {
            character.updateEmotionalState('aggressive');
        }

        // 显示反击对话（降低频率）
        const dialogue = this.getRandomDialogue('counterAttack');
        this.speakWithControl(dialogue, character, 'normal', 200);

        console.log('⚡ 反击成功！');
    }

    /**
     * 执行受击动作
     * @param {Object} character - 功夫狗角色对象
     */
    performHurt(character) {
        console.log('💥 执行受击动作');
        this.battleStats.successfulHits++;

        // 使用角色的生命值系统减少生命值
        if (character.takeDamage) {
            character.takeDamage(1);
        }
        const health = character.getHealth ? character.getHealth() : 0;
        const maxHealth = character.getMaxHealth ? character.getMaxHealth() : 20;
        console.log(`❤️ 生命值: ${health}/${maxHealth}`);

        // 播放受击动画（不循环，动画控制器会自动判断）
        this.animationController.playAction(this.animations.hurt);

        // 播放受击音效
        this.audioSystem.playSound(this.sounds.hurt);

        // 显示受击对话（降低频率）
        const dialogue = this.getRandomDialogue('hurt');
        this.speakWithControl(dialogue, character, 'normal', 300);

        // 检查是否需要倒下
        if (health <= 0) {
            setTimeout(() => {
                this.performDown(character);
            }, 2500); // 等待受击动画播放完毕
        }

        console.log('💔 受击成功！生命值减少1点');
    }
    
    /**
     * 执行倒地动作
     * @param {Object} character - 功夫狗角色对象
     */
    performDown(character) {
        console.log('💀 执行倒地动作');

        // 使用角色的生命值系统设置倒下状态
        if (character.setDown) {
            character.setDown(true);
        }

        // 播放倒地动画（不循环，动画控制器会自动判断）
        this.animationController.playAction(this.animations.down);

        // 播放倒地音效
        this.audioSystem.playSound(this.sounds.down);

        // 显示失败对话（重要事件，保持语音）
        const dialogue = this.getRandomDialogue('down');
        this.speakWithControl(dialogue, character, 'important', 500);

        console.log('💀 功夫狗倒下了！');

        // 5秒后自动复活
        setTimeout(() => {
            this.revive(character);
        }, 5000);
    }
    
    /**
     * 复活功夫狗
     * @param {Object} character - 功夫狗角色对象
     */
    revive(character) {
        console.log('✨ 功夫狗复活了！');

        // 使用角色的生命值系统完全恢复
        if (character.fullHeal) {
            character.fullHeal();
        }

        // 播放站立动画
        this.animationController.playAction('stand');

        // 复活对话（重要事件，保持语音）
        this.speakWithControl('我又满血复活了！继续来战吧！', character, 'important', 1000);

        this.updateBattleUI(character);
    }
    
    /**
     * 更新连击计数
     * @param {number} currentTime - 当前时间
     */
    updateComboCount(currentTime) {
        // 检查是否在连击时间窗口内
        if (currentTime - this.comboSystem.lastClickTime <= this.comboSystem.comboTimeout) {
            this.comboSystem.count = Math.min(this.comboSystem.count + 1, this.comboSystem.maxCombo);
        } else {
            this.comboSystem.count = 1; // 重新开始连击
        }

        this.comboSystem.lastClickTime = currentTime;

        // 连击特效
        if (this.comboSystem.count > 1) {
            console.log(`🔥 连击 x${this.comboSystem.count}!`);

            // 根据连击数显示不同的反馈
            if (this.comboSystem.count % 10 === 0) {
                console.log(`🎉 连击达到 ${this.comboSystem.count}! 太棒了！`);

                // 连击里程碑语音
                const comboDialogues = [
                    `连击 ${this.comboSystem.count}! 厉害！`,
                    `哇！${this.comboSystem.count} 连击！`,
                    `太快了！${this.comboSystem.count} 连击！`,
                    `${this.comboSystem.count} 连击！我都跟不上了！`,
                    `连击 ${this.comboSystem.count}！你是高手！`
                ];
                const dialogue = comboDialogues[Math.floor(Math.random() * comboDialogues.length)];

                // 使用延迟获取character参数
                setTimeout(() => {
                    if (this.lastCharacter) {
                        this.speakWithControl(dialogue, this.lastCharacter, 'combo', 0);
                    }
                }, 100);
            }
        }
    }

    /**
     * 判断是否应该说话
     * @param {string} speechType - 语音类型 ('normal', 'important', 'combo')
     * @param {number} currentTime - 当前时间
     * @returns {boolean} 是否应该说话
     */
    shouldSpeak(speechType = 'normal', currentTime = Date.now()) {
        // 重要事件（倒下、复活）总是说话
        if (speechType === 'important') {
            return true;
        }

        // 连击里程碑语音
        if (speechType === 'combo') {
            return this.comboSystem.count % this.speechControl.comboSpeechInterval === 0;
        }

        // 检查语音冷却时间
        if (currentTime - this.speechControl.lastSpeechTime < this.speechControl.speechCooldown) {
            return false;
        }

        // 常规战斗动作概率性语音
        if (speechType === 'normal') {
            return Math.random() < this.speechControl.speechProbability;
        }

        return false;
    }

    /**
     * 安全的语音播放（带频率控制）
     * @param {string} text - 要说的话
     * @param {Object} character - 角色对象
     * @param {string} speechType - 语音类型
     * @param {number} delay - 延迟时间
     */
    speakWithControl(text, character, speechType = 'normal', delay = 0) {
        const currentTime = Date.now();

        if (this.shouldSpeak(speechType, currentTime)) {
            if (delay > 0) {
                setTimeout(() => {
                    this.speechSystem.speak(text, character);
                }, delay);
            } else {
                this.speechSystem.speak(text, character);
            }

            // 更新最后语音时间（仅对非重要事件）
            if (speechType !== 'important') {
                this.speechControl.lastSpeechTime = currentTime;
            }
        }
    }

    /**
     * 获取随机对话
     * @param {string} type - 对话类型
     * @returns {string} 随机对话内容
     */
    getRandomDialogue(type) {
        const dialogues = this.dialogues[type];
        if (!dialogues || dialogues.length === 0) {
            return `${type} 动作完成！`;
        }

        return dialogues[Math.floor(Math.random() * dialogues.length)];
    }
    
    /**
     * 创建战斗UI界面
     */
    createBattleUI() {
        // 检查是否已存在
        if (document.getElementById('battle-ui')) return;
        
        const battleUI = document.createElement('div');
        battleUI.id = 'battle-ui';
        battleUI.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            min-width: 200px;
            z-index: 100;
            border: 2px solid #ff6b6b;
        `;
        
        battleUI.innerHTML = `
            <h3 style="margin: 0 0 10px 0; color: #ff6b6b;">⚔️ 战斗状态</h3>
            <div id="health-display">❤️ 生命值: 20/20</div>
            <div id="status-display">🟢 状态: 正常</div>
            <div id="combo-display" style="margin-top: 5px; font-size: 14px; color: #FFD700; font-weight: bold;">
                🔥 连击: <span id="combo-count">0</span>
            </div>
            <div style="margin-top: 10px; font-size: 12px; color: #ccc;">
                <div>连续快速点击功夫狗！</div>
                <div id="block-chance-display">🛡️ 格挡概率: 40%</div>
                <div id="counter-chance-display">⚡ 反击概率: 50%</div>
                <div style="font-size: 10px; color: #999; margin-top: 5px;">📊 概率受情绪状态影响</div>
            </div>
            <div id="battle-stats" style="margin-top: 10px; font-size: 11px; color: #aaa;">
                <div>总攻击: <span id="total-attacks">0</span></div>
                <div>格挡: <span id="blocked-attacks">0</span></div>
                <div>命中: <span id="successful-hits">0</span></div>
                <div>反击: <span id="counter-attacks">0</span></div>
            </div>
        `;
        
        document.body.appendChild(battleUI);
    }
    
    /**
     * 更新战斗UI
     * @param {Object} character - 功夫狗角色对象
     */
    updateBattleUI(character = null) {
        const healthDisplay = document.getElementById('health-display');
        const statusDisplay = document.getElementById('status-display');
        const comboCount = document.getElementById('combo-count');
        const blockChanceDisplay = document.getElementById('block-chance-display');
        const counterChanceDisplay = document.getElementById('counter-chance-display');
        const totalAttacks = document.getElementById('total-attacks');
        const blockedAttacks = document.getElementById('blocked-attacks');
        const successfulHits = document.getElementById('successful-hits');
        const counterAttacks = document.getElementById('counter-attacks');

        // 获取生命值信息
        const health = character && character.getHealth ? character.getHealth() : 20;
        const maxHealth = character && character.getMaxHealth ? character.getMaxHealth() : 20;
        const isDown = character && character.isDownState ? character.isDownState() : false;

        if (healthDisplay) {
            healthDisplay.textContent = `❤️ 生命值: ${health}/${maxHealth}`;
        }

        if (statusDisplay) {
            if (isDown) {
                statusDisplay.textContent = '💀 状态: 倒下';
                statusDisplay.style.color = '#ff6b6b';
            } else if (health <= 1) {
                statusDisplay.textContent = '⚠️ 状态: 危险';
                statusDisplay.style.color = '#ffa500';
            } else {
                statusDisplay.textContent = '🟢 状态: 正常';
                statusDisplay.style.color = '#4CAF50';
            }
        }

        // 更新连击显示
        if (comboCount) {
            comboCount.textContent = this.comboSystem.count;

            // 根据连击数改变颜色
            const comboDisplay = document.getElementById('combo-display');
            if (comboDisplay) {
                if (this.comboSystem.count >= 20) {
                    comboDisplay.style.color = '#FF4444'; // 红色 - 超高连击
                } else if (this.comboSystem.count >= 10) {
                    comboDisplay.style.color = '#FF6B35'; // 橙色 - 高连击
                } else if (this.comboSystem.count >= 5) {
                    comboDisplay.style.color = '#FFD700'; // 金色 - 中等连击
                } else {
                    comboDisplay.style.color = '#FFFFFF'; // 白色 - 低连击
                }
            }
        }

        // 更新动态概率显示
        if (blockChanceDisplay && character) {
            const currentBlockChance = this.calculateBlockChance(character);
            blockChanceDisplay.textContent = `🛡️ 格挡概率: ${(currentBlockChance * 100).toFixed(1)}%`;

            // 根据概率高低改变颜色
            if (currentBlockChance > 0.6) {
                blockChanceDisplay.style.color = '#4CAF50'; // 绿色 - 高概率
            } else if (currentBlockChance > 0.4) {
                blockChanceDisplay.style.color = '#FFD700'; // 金色 - 中等概率
            } else {
                blockChanceDisplay.style.color = '#FF6B35'; // 橙色 - 低概率
            }
        }

        if (counterChanceDisplay && character) {
            const currentCounterChance = this.calculateCounterAttackChance(character);
            counterChanceDisplay.textContent = `⚡ 反击概率: ${(currentCounterChance * 100).toFixed(1)}%`;

            // 根据概率高低改变颜色
            if (currentCounterChance > 0.7) {
                counterChanceDisplay.style.color = '#FF4444'; // 红色 - 高反击
            } else if (currentCounterChance > 0.5) {
                counterChanceDisplay.style.color = '#FFD700'; // 金色 - 中等反击
            } else {
                counterChanceDisplay.style.color = '#4ECDC4'; // 青色 - 低反击
            }
        }

        if (totalAttacks) totalAttacks.textContent = this.battleStats.totalAttacks;
        if (blockedAttacks) blockedAttacks.textContent = this.battleStats.blockedAttacks;
        if (successfulHits) successfulHits.textContent = this.battleStats.successfulHits;
        if (counterAttacks) counterAttacks.textContent = this.battleStats.counterAttacks;
    }
    
    /**
     * 获取战斗状态
     * @param {Object} character - 功夫狗角色对象
     * @returns {Object} 当前战斗状态
     */
    getBattleStatus(character = null) {
        const health = character && character.getHealth ? character.getHealth() : 20;
        const maxHealth = character && character.getMaxHealth ? character.getMaxHealth() : 20;
        const isDown = character && character.isDownState ? character.isDownState() : false;

        return {
            health: health,
            maxHealth: maxHealth,
            isDown: isDown,
            isBattleMode: this.isBattleMode,
            stats: { ...this.battleStats }
        };
    }

    /**
     * 重置战斗状态
     * @param {Object} character - 功夫狗角色对象
     */
    resetBattle(character = null) {
        // 使用角色的生命值系统重置
        if (character && character.fullHeal) {
            character.fullHeal();
        }

        this.battleStats = {
            totalAttacks: 0,
            blockedAttacks: 0,
            successfulHits: 0,
            counterAttacks: 0
        };

        // 重置连击计数
        this.comboSystem.count = 0;
        this.comboSystem.lastClickTime = 0;

        this.updateBattleUI(character);
        console.log('🔄 战斗状态已重置');
    }
    
    /**
     * 销毁战斗系统
     */
    dispose() {
        const battleUI = document.getElementById('battle-ui');
        if (battleUI && battleUI.parentNode) {
            battleUI.parentNode.removeChild(battleUI);
        }
        console.log('🗑️ 战斗系统已销毁');
    }
}
