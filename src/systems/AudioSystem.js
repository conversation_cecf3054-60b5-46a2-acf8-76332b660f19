/**
 * 音效系统
 * 为功夫狗的动作和对话添加音效
 */

export class AudioSystem {
    constructor() {
        this.audioContext = null;
        this.sounds = new Map();
        this.isEnabled = true;
        this.volume = 0.5;
        
        this.init();
    }
    
    async init() {
        try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建程序化音效
            this.createProgrammaticSounds();
            
            // console.log('🔊 音效系统初始化完成');
        } catch (error) {
            console.warn('⚠️ 音效系统初始化失败:', error);
            this.isEnabled = false;
        }
    }
    
    /**
     * 创建程序化音效
     */
    createProgrammaticSounds() {
        // 出拳音效
        this.sounds.set('punch', () => this.createPunchSound());
        
        // 踢腿音效
        this.sounds.set('kick', () => this.createKickSound());
        
        // 跳跃音效
        this.sounds.set('jump', () => this.createJumpSound());
        
        // 翻滚音效
        this.sounds.set('roll', () => this.createRollSound());
        
        // 抱拳音效
        this.sounds.set('bow', () => this.createBowSound());
        
        // 脚步声
        this.sounds.set('walk', () => this.createWalkSound());
        
        // 坐下音效
        this.sounds.set('sit', () => this.createSitSound());

        // 战斗音效
        this.sounds.set('block-sound', () => this.createBlockSound());
        this.sounds.set('hurt-sound', () => this.createHurtSound());
        this.sounds.set('down-sound', () => this.createDownSound());

        // console.log('🎵 程序化音效创建完成');
    }
    
    /**
     * 播放音效
     */
    playSound(soundName) {
        if (!this.isEnabled || !this.audioContext) return;
        
        const soundGenerator = this.sounds.get(soundName);
        if (soundGenerator) {
            try {
                soundGenerator();
                console.log(`🔊 播放音效: ${soundName}`);
            } catch (error) {
                console.warn(`⚠️ 音效播放失败: ${soundName}`, error);
            }
        }
    }
    
    /**
     * 创建出拳音效
     */
    createPunchSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // 快速的低频冲击声
        oscillator.frequency.setValueAtTime(80, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(40, this.audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(this.volume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.15);
        
        oscillator.type = 'square';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.15);
    }
    
    /**
     * 创建踢腿音效
     */
    createKickSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // 更高频的冲击声
        oscillator.frequency.setValueAtTime(120, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(60, this.audioContext.currentTime + 0.12);
        
        gainNode.gain.setValueAtTime(this.volume * 0.8, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.18);
        
        oscillator.type = 'sawtooth';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.18);
    }
    
    /**
     * 创建跳跃音效
     */
    createJumpSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // 上升的音调
        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.3);
        
        gainNode.gain.setValueAtTime(this.volume * 0.6, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
        
        oscillator.type = 'sine';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.3);
    }
    
    /**
     * 创建翻滚音效
     */
    createRollSound() {
        // 创建多个振荡器模拟翻滚声
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(this.audioContext.destination);
                
                oscillator.frequency.setValueAtTime(60 + i * 20, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(30 + i * 10, this.audioContext.currentTime + 0.2);
                
                gainNode.gain.setValueAtTime(this.volume * 0.4, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
                
                oscillator.type = 'triangle';
                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.2);
            }, i * 100);
        }
    }
    
    /**
     * 创建抱拳音效
     */
    createBowSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // 庄重的低音
        oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime);
        oscillator.frequency.linearRampToValueAtTime(100, this.audioContext.currentTime + 0.5);
        
        gainNode.gain.setValueAtTime(this.volume * 0.5, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
        
        oscillator.type = 'sine';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }
    
    /**
     * 创建脚步声
     */
    createWalkSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // 短促的脚步声
        oscillator.frequency.setValueAtTime(100, this.audioContext.currentTime);
        
        gainNode.gain.setValueAtTime(this.volume * 0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.type = 'square';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }
    
    /**
     * 创建坐下音效
     */
    createSitSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // 下降的音调
        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(80, this.audioContext.currentTime + 0.4);
        
        gainNode.gain.setValueAtTime(this.volume * 0.4, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.4);
        
        oscillator.type = 'triangle';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.4);
    }

    /**
     * 创建格挡音效
     */
    createBlockSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // 金属碰撞声 - 高频短促
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, this.audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(this.volume * 0.6, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);

        oscillator.type = 'square';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.2);
    }

    /**
     * 创建受击音效
     */
    createHurtSound() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // 沉闷的撞击声
        oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(80, this.audioContext.currentTime + 0.3);

        gainNode.gain.setValueAtTime(this.volume * 0.7, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.4);

        oscillator.type = 'sawtooth';
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.4);
    }

    /**
     * 创建倒地音效
     */
    createDownSound() {
        // 创建多层音效模拟倒地声
        const oscillator1 = this.audioContext.createOscillator();
        const oscillator2 = this.audioContext.createOscillator();
        const gainNode1 = this.audioContext.createGain();
        const gainNode2 = this.audioContext.createGain();

        oscillator1.connect(gainNode1);
        oscillator2.connect(gainNode2);
        gainNode1.connect(this.audioContext.destination);
        gainNode2.connect(this.audioContext.destination);

        // 第一层：低频撞击
        oscillator1.frequency.setValueAtTime(60, this.audioContext.currentTime);
        oscillator1.frequency.exponentialRampToValueAtTime(30, this.audioContext.currentTime + 0.5);

        gainNode1.gain.setValueAtTime(this.volume * 0.8, this.audioContext.currentTime);
        gainNode1.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.6);

        // 第二层：中频回响
        oscillator2.frequency.setValueAtTime(200, this.audioContext.currentTime + 0.1);
        oscillator2.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.4);

        gainNode2.gain.setValueAtTime(this.volume * 0.4, this.audioContext.currentTime + 0.1);
        gainNode2.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);

        oscillator1.type = 'square';
        oscillator2.type = 'triangle';

        oscillator1.start(this.audioContext.currentTime);
        oscillator2.start(this.audioContext.currentTime + 0.1);
        oscillator1.stop(this.audioContext.currentTime + 0.6);
        oscillator2.stop(this.audioContext.currentTime + 0.5);
    }

    /**
     * 设置音量
     */
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
    }
    
    /**
     * 启用/禁用音效
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }
    
    /**
     * 检查音效是否启用
     */
    isAudioEnabled() {
        return this.isEnabled && this.audioContext;
    }
    
    /**
     * 恢复音频上下文（用户交互后）
     */
    async resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            try {
                await this.audioContext.resume();
                console.log('🔊 音频上下文已恢复');
            } catch (error) {
                console.warn('⚠️ 音频上下文恢复失败:', error);
            }
        }
    }
    
    /**
     * 清理资源
     */
    dispose() {
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.sounds.clear();
        console.log('🗑️ 音效系统已清理');
    }
}
