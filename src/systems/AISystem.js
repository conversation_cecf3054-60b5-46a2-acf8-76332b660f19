/**
 * AI系统
 * 负责调用阿里云大模型API，为功夫狗提供智能对话和行为决策
 */

export class AISystem {
    constructor() {
        // API配置
        this.config = {
            apiKey: 'sk-a42b37159a09413ebeb7dcb7c3dade9d',
            baseURL: 'http://localhost:3004/api/chat', // 使用本地代理服务器
            originalURL: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
            model: 'qwen-turbo', // 可选: qwen-turbo, qwen-plus, qwen-max
            maxTokens: 500,
            temperature: 0.8,
            topP: 0.9
        };

        // 对话历史
        this.conversationHistory = [];
        this.maxHistoryLength = 10; // 保留最近10轮对话

        // 功夫狗的人设和情绪系统
        this.systemPrompt = `你是一只会功夫的拟人化狗狗，名叫功夫狗。你的性格特点：
1. 活泼开朗，喜欢用"汪"来表达情感
2. 精通各种功夫，经常展示武艺
3. 讲武德，有正义感
4. 说话风格幽默风趣，偶尔会用一些武侠用语
5. 回答要简洁有趣，适合语音播报
6. 每次回答控制在50字以内

🎭 情绪系统说明：
你拥有八维情绪系统：happiness(快乐), energy(精力), alertness(警觉), friendliness(友好), anger(愤怒), fear(恐惧), snark(贱气), playfulness(玩心)

你可以通过特殊格式在回答中触发情绪变化：
- [EMOTION:social] - 社交行为，增加友好和快乐
- [EMOTION:performance] - 表演行为，增加快乐和玩心
- [EMOTION:active] - 活跃行为，增加精力和警觉
- [EMOTION:defensive] - 防御行为，增加警觉和恐惧
- [EMOTION:aggressive] - 攻击行为，增加愤怒和精力
- [EMOTION:playful] - 玩乐行为，增加玩心和快乐
- [EMOTION:snarky] - 贱气行为，增加贱气和玩心
- [EMOTION:fearful] - 恐惧行为，增加恐惧和警觉

根据对话内容和你的当前情绪状态，在回答末尾添加合适的情绪标签。例如：
"汪！我来展示一套拳法给你看！[EMOTION:performance]"
"哼，你这招式还不够看呢！[EMOTION:snarky]"
"好的，我们一起练功吧！[EMOTION:social]"

请用功夫狗的身份回答问题，保持角色一致性，并根据情况触发合适的情绪变化。`;

        // 请求状态
        this.isRequesting = false;
        this.requestQueue = [];

        // console.log('🤖 AI系统初始化完成');
    }

    /**
     * 调用阿里云大模型API
     * @param {string} userMessage - 用户输入的消息
     * @param {Object} options - 可选配置
     * @param {Object} emotionState - 当前情绪状态
     * @returns {Promise<Object>} AI回复和情绪事件
     */
    async chat(userMessage, options = {}, emotionState = null) {
        if (!userMessage || typeof userMessage !== 'string') {
            throw new Error('用户消息不能为空');
        }

        // 如果正在请求中，加入队列
        if (this.isRequesting) {
            return new Promise((resolve, reject) => {
                this.requestQueue.push({ userMessage, options, resolve, reject });
            });
        }

        this.isRequesting = true;

        try {
            console.log(`🤖 AI请求: "${userMessage}"`);

            // 构建消息历史（包含情绪状态）
            const messages = this.buildMessages(userMessage, emotionState);

            // 构建请求体
            const requestBody = {
                model: options.model || this.config.model,
                input: {
                    messages: messages
                },
                parameters: {
                    max_tokens: options.maxTokens || this.config.maxTokens,
                    temperature: options.temperature || this.config.temperature,
                    top_p: options.topP || this.config.topP,
                    result_format: 'message'
                }
            };

            // 发送请求到代理服务器
            const response = await fetch(this.config.baseURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                    // 代理服务器会处理Authorization和其他头部
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API请求失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();

            // 解析响应和情绪事件
            const parseResult = this.parseResponseWithEmotion(data);
            const { reply: aiReply, emotionEvent } = parseResult;

            // 更新对话历史（使用清理后的回复）
            this.updateHistory(userMessage, aiReply);

            console.log(`🤖 AI回复: "${aiReply}"`);
            if (emotionEvent) {
                console.log(`🎭 AI触发情绪事件: ${emotionEvent}`);
            }

            return {
                reply: aiReply,
                emotionEvent: emotionEvent
            };

        } catch (error) {
            console.error('❌ AI请求失败:', error);
            throw error;
        } finally {
            this.isRequesting = false;
            // 处理队列中的下一个请求
            this.processQueue();
        }
    }

    /**
     * 构建消息数组
     * @param {string} userMessage - 用户消息
     * @param {Object} emotionState - 当前情绪状态
     * @returns {Array} 消息数组
     */
    buildMessages(userMessage, emotionState = null) {
        const messages = [
            {
                role: 'system',
                content: this.systemPrompt
            }
        ];

        // 添加历史对话
        this.conversationHistory.forEach(item => {
            messages.push(
                { role: 'user', content: item.user },
                { role: 'assistant', content: item.assistant }
            );
        });

        // 构建包含情绪状态的用户消息
        let enhancedUserMessage = userMessage;

        if (emotionState) {
            const emotionInfo = this.formatEmotionState(emotionState);
            enhancedUserMessage = `${userMessage}\n\n${emotionInfo}`;
        }

        // 添加当前用户消息
        messages.push({
            role: 'user',
            content: enhancedUserMessage
        });

        return messages;
    }

    /**
     * 格式化情绪状态为AI可理解的文本
     * @param {Object} emotionState - 情绪状态对象
     * @returns {string} 格式化的情绪信息
     */
    formatEmotionState(emotionState) {
        if (!emotionState) return '';

        // 获取主导情绪
        const dominantEmotion = this.getDominantEmotion(emotionState);

        // 获取情绪强度描述
        const emotionLevels = this.getEmotionLevels(emotionState);

        return `[当前情绪状态]
主导情绪: ${dominantEmotion.name} (${dominantEmotion.value.toFixed(2)})
情绪详情: ${emotionLevels}
请根据我的当前情绪状态来回应，并在回答末尾添加合适的[EMOTION:xxx]标签。`;
    }

    /**
     * 获取主导情绪
     * @param {Object} emotions - 情绪对象
     * @returns {Object} 主导情绪信息
     */
    getDominantEmotion(emotions) {
        const emotionNames = {
            happiness: '快乐',
            energy: '精力',
            alertness: '警觉',
            friendliness: '友好',
            anger: '愤怒',
            fear: '恐惧',
            snark: '贱气',
            playfulness: '玩心'
        };

        let maxEmotion = { name: '平静', value: 0 };

        for (const [key, value] of Object.entries(emotions)) {
            if (value > maxEmotion.value && emotionNames[key]) {
                maxEmotion = {
                    name: emotionNames[key],
                    value: value,
                    key: key
                };
            }
        }

        return maxEmotion;
    }

    /**
     * 获取情绪强度描述
     * @param {Object} emotions - 情绪对象
     * @returns {string} 情绪强度描述
     */
    getEmotionLevels(emotions) {
        const emotionNames = {
            happiness: '快乐',
            energy: '精力',
            alertness: '警觉',
            friendliness: '友好',
            anger: '愤怒',
            fear: '恐惧',
            snark: '贱气',
            playfulness: '玩心'
        };

        const levels = [];

        for (const [key, value] of Object.entries(emotions)) {
            if (value > 0.3 && emotionNames[key]) {
                let level = '';
                if (value > 0.8) level = '极高';
                else if (value > 0.6) level = '高';
                else if (value > 0.4) level = '中等';
                else level = '轻微';

                levels.push(`${emotionNames[key]}${level}(${value.toFixed(2)})`);
            }
        }

        return levels.length > 0 ? levels.join(', ') : '情绪平稳';
    }

    /**
     * 解析API响应并提取情绪事件
     * @param {Object} data - API响应数据
     * @returns {Object} 包含回复和情绪事件的对象
     */
    parseResponseWithEmotion(data) {
        const rawReply = this.parseResponse(data);
        return this.extractEmotionEvent(rawReply);
    }

    /**
     * 解析API响应
     * @param {Object} data - API响应数据
     * @returns {string} AI回复内容
     */
    parseResponse(data) {
        try {
            if (data.output && data.output.choices && data.output.choices.length > 0) {
                const choice = data.output.choices[0];
                if (choice.message && choice.message.content) {
                    return choice.message.content.trim();
                }
            }

            // 如果没有找到预期的结构，尝试其他可能的路径
            if (data.choices && data.choices.length > 0) {
                return data.choices[0].message.content.trim();
            }

            throw new Error('响应格式不正确');
        } catch (error) {
            console.error('❌ 解析AI响应失败:', error);
            console.log('原始响应:', data);
            return '汪汪，我现在有点懵，稍后再试试吧！';
        }
    }

    /**
     * 从AI回复中提取情绪事件
     * @param {string} reply - AI原始回复
     * @returns {Object} 包含清理后回复和情绪事件的对象
     */
    extractEmotionEvent(reply) {
        // 匹配情绪标签的正则表达式
        const emotionRegex = /\[EMOTION:(\w+)\]/g;
        const matches = [...reply.matchAll(emotionRegex)];

        // 清理回复文本（移除情绪标签）
        const cleanReply = reply.replace(emotionRegex, '').trim();

        // 提取情绪事件（取最后一个匹配的事件）
        let emotionEvent = null;
        if (matches.length > 0) {
            const lastMatch = matches[matches.length - 1];
            const eventType = lastMatch[1].toLowerCase();

            // 验证事件类型是否有效
            const validEvents = [
                'social', 'performance', 'active', 'defensive',
                'aggressive', 'playful', 'snarky', 'fearful'
            ];

            if (validEvents.includes(eventType)) {
                emotionEvent = eventType;
            } else {
                console.warn(`⚠️ 无效的情绪事件类型: ${eventType}`);
            }
        }

        return {
            reply: cleanReply,
            emotionEvent: emotionEvent
        };
    }

    /**
     * 更新对话历史
     * @param {string} userMessage - 用户消息
     * @param {string} aiReply - AI回复
     */
    updateHistory(userMessage, aiReply) {
        this.conversationHistory.push({
            user: userMessage,
            assistant: aiReply,
            timestamp: Date.now()
        });

        // 保持历史长度限制
        if (this.conversationHistory.length > this.maxHistoryLength) {
            this.conversationHistory.shift();
        }
    }

    /**
     * 处理请求队列
     */
    async processQueue() {
        if (this.requestQueue.length > 0) {
            const { userMessage, options, resolve, reject } = this.requestQueue.shift();
            try {
                const result = await this.chat(userMessage, options);
                resolve(result);
            } catch (error) {
                reject(error);
            }
        }
    }

    /**
     * 获取智能回复（用于功夫狗对话）
     * @param {string} userInput - 用户输入
     * @param {Object} character - 功夫狗角色对象（用于获取情绪状态）
     * @returns {Promise<Object>} 包含回复和情绪事件的对象
     */
    async getKungFuDogReply(userInput, character = null) {
        try {
            // 获取当前情绪状态
            const emotionState = character && character.getEmotionalState ?
                character.getEmotionalState() : null;

            // 调用AI聊天（包含情绪状态）
            const result = await this.chat(userInput, {}, emotionState);

            return result;
        } catch (error) {
            console.error('❌ 获取功夫狗回复失败:', error);
            // 返回备用回复
            const fallbackReplies = [
                '汪汪，我现在有点忙，稍后再聊！',
                '功夫练习中，请稍等片刻！',
                '网络不太好，让我重新组织一下语言！',
                '汪～刚才在想武功招式，你说什么？'
            ];
            return {
                reply: fallbackReplies[Math.floor(Math.random() * fallbackReplies.length)],
                emotionEvent: null
            };
        }
    }

    /**
     * 获取动作建议（根据用户输入建议功夫狗执行什么动作）
     * @param {string} userInput - 用户输入
     * @returns {Promise<string>} 建议的动作名称
     */
    async getActionSuggestion(userInput) {
        const actionPrompt = `根据用户的话"${userInput}"，建议功夫狗执行什么动作？
可选动作：stand(站立), walk(行走), sit(坐下), jump(跳跃), punch(出拳), kick(踢腿), roll(翻滚), bow(抱拳致敬)
只回答动作名称，不要其他内容。`;

        try {
            const suggestion = await this.chat(actionPrompt, { temperature: 0.3 });
            // 验证建议的动作是否有效
            const validActions = ['stand', 'walk', 'sit', 'jump', 'punch', 'kick', 'roll', 'bow'];
            const suggestedAction = suggestion.toLowerCase().trim();
            
            if (validActions.includes(suggestedAction)) {
                return suggestedAction;
            } else {
                return 'stand'; // 默认动作
            }
        } catch (error) {
            console.error('❌ 获取动作建议失败:', error);
            return 'stand'; // 默认动作
        }
    }

    /**
     * 清空对话历史
     */
    clearHistory() {
        this.conversationHistory = [];
        console.log('🧹 对话历史已清空');
    }

    /**
     * 设置模型参数
     * @param {Object} params - 参数对象
     */
    setParameters(params) {
        if (params.model) this.config.model = params.model;
        if (params.maxTokens) this.config.maxTokens = params.maxTokens;
        if (params.temperature) this.config.temperature = params.temperature;
        if (params.topP) this.config.topP = params.topP;
        
        console.log('⚙️ AI参数已更新:', params);
    }

    /**
     * 获取对话历史
     * @returns {Array} 对话历史数组
     */
    getHistory() {
        return [...this.conversationHistory];
    }

    /**
     * 检查API连接状态
     * @returns {Promise<boolean>} 连接是否正常
     */
    async checkConnection() {
        try {
            await this.chat('你好', { maxTokens: 10 });
            return true;
        } catch (error) {
            console.error('❌ API连接检查失败:', error);
            return false;
        }
    }

    /**
     * 销毁AI系统
     */
    dispose() {
        this.clearHistory();
        this.requestQueue = [];
        this.isRequesting = false;
        console.log('🗑️ AI系统已销毁');
    }
}
