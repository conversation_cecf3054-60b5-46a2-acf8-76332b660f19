/**
 * 粒子系统
 * 为功夫狗的动作添加特效，增强视觉效果
 */

import * as THREE from 'three';

export class ParticleSystem {
    constructor(scene) {
        this.scene = scene;
        this.particleGroups = new Map();
        
        // 粒子材质
        this.materials = {
            spark: new THREE.PointsMaterial({
                color: 0xFFD700,
                size: 0.1,
                transparent: true,
                opacity: 0.8,
                blending: THREE.AdditiveBlending
            }),
            dust: new THREE.PointsMaterial({
                color: 0x8B4513,
                size: 0.05,
                transparent: true,
                opacity: 0.6
            }),
            energy: new THREE.PointsMaterial({
                color: 0x00FFFF,
                size: 0.15,
                transparent: true,
                opacity: 0.9,
                blending: THREE.AdditiveBlending
            })
        };
        
        // console.log('✨ 粒子系统初始化完成');
    }

    /**
     * 创建快速点击反馈特效
     * @param {THREE.Vector3} position - 特效位置
     */
    createClickFeedback(position) {
        const particleCount = 10;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const velocities = new Float32Array(particleCount * 3);

        // 创建粒子位置和速度
        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // 在点击位置周围随机分布
            positions[i3] = position.x + (Math.random() - 0.5) * 0.2;
            positions[i3 + 1] = position.y + (Math.random() - 0.5) * 0.2;
            positions[i3 + 2] = position.z + (Math.random() - 0.5) * 0.2;

            // 随机速度
            velocities[i3] = (Math.random() - 0.5) * 2;
            velocities[i3 + 1] = Math.random() * 2;
            velocities[i3 + 2] = (Math.random() - 0.5) * 2;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        // 创建快速消失的材质
        const material = new THREE.PointsMaterial({
            color: 0xFFFFFF,
            size: 0.08,
            transparent: true,
            opacity: 1.0,
            blending: THREE.AdditiveBlending
        });

        const particles = new THREE.Points(geometry, material);
        this.scene.add(particles);

        // 快速动画和清理
        const startTime = Date.now();
        const duration = 300; // 300ms快速消失

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress >= 1) {
                this.scene.remove(particles);
                geometry.dispose();
                material.dispose();
                return;
            }

            // 更新粒子位置
            const positions = geometry.attributes.position.array;
            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;
                positions[i3] += velocities[i3] * 0.02;
                positions[i3 + 1] += velocities[i3 + 1] * 0.02;
                positions[i3 + 2] += velocities[i3 + 2] * 0.02;
            }
            geometry.attributes.position.needsUpdate = true;

            // 淡出效果
            material.opacity = 1 - progress;

            requestAnimationFrame(animate);
        };

        animate();
    }

    /**
     * 更新粒子系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime = 0.016) {
        // 粒子系统的更新逻辑
        // 目前粒子系统使用自己的动画循环，这里可以添加全局更新逻辑
    }
    
    /**
     * 创建出拳特效
     */
    createPunchEffect(position) {
        const particles = this.createParticles(20, 'spark');
        particles.position.copy(position);
        
        this.animateParticles(particles, {
            spread: 2,
            speed: 5,
            life: 1000,
            gravity: -2
        });
        
        console.log('👊 出拳特效');
    }
    
    /**
     * 创建踢腿特效
     */
    createKickEffect(position) {
        const particles = this.createParticles(30, 'energy');
        particles.position.copy(position);
        
        this.animateParticles(particles, {
            spread: 3,
            speed: 8,
            life: 1200,
            gravity: -1
        });
        
        console.log('🦵 踢腿特效');
    }
    
    /**
     * 创建跳跃灰尘特效
     */
    createJumpDustEffect(position) {
        const particles = this.createParticles(15, 'dust');
        particles.position.copy(position);
        particles.position.y = 0.1; // 地面高度
        
        this.animateParticles(particles, {
            spread: 1.5,
            speed: 3,
            life: 800,
            gravity: -0.5
        });
        
        console.log('💨 跳跃灰尘特效');
    }
    
    /**
     * 创建翻滚特效
     */
    createRollEffect(position) {
        const particles = this.createParticles(25, 'dust');
        particles.position.copy(position);
        
        this.animateParticles(particles, {
            spread: 2,
            speed: 4,
            life: 1500,
            gravity: -1,
            spiral: true
        });
        
        console.log('🌪️ 翻滚特效');
    }
    
    /**
     * 创建粒子组
     */
    createParticles(count, materialType) {
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(count * 3);
        const velocities = new Float32Array(count * 3);
        
        // 初始化粒子位置和速度
        for (let i = 0; i < count; i++) {
            const i3 = i * 3;
            
            // 随机初始位置
            positions[i3] = (Math.random() - 0.5) * 0.2;
            positions[i3 + 1] = Math.random() * 0.2;
            positions[i3 + 2] = (Math.random() - 0.5) * 0.2;
            
            // 随机初始速度
            velocities[i3] = (Math.random() - 0.5) * 2;
            velocities[i3 + 1] = Math.random() * 2 + 1;
            velocities[i3 + 2] = (Math.random() - 0.5) * 2;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
        
        const material = this.materials[materialType] || this.materials.spark;
        const particles = new THREE.Points(geometry, material);
        
        this.scene.add(particles);
        
        return particles;
    }
    
    /**
     * 动画化粒子
     */
    animateParticles(particles, config) {
        const startTime = Date.now();
        const positions = particles.geometry.attributes.position.array;
        const velocities = particles.geometry.attributes.velocity.array;
        const initialPositions = [...positions];
        const initialVelocities = [...velocities];
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / config.life;
            
            if (progress >= 1) {
                // 动画结束，移除粒子
                this.scene.remove(particles);
                particles.geometry.dispose();
                return;
            }
            
            // 更新粒子位置
            for (let i = 0; i < positions.length; i += 3) {
                const index = i / 3;
                const time = elapsed / 1000;
                
                // 基础运动
                positions[i] = initialPositions[i] + initialVelocities[i] * time * config.speed;
                positions[i + 1] = initialPositions[i + 1] + initialVelocities[i + 1] * time * config.speed + 0.5 * config.gravity * time * time;
                positions[i + 2] = initialPositions[i + 2] + initialVelocities[i + 2] * time * config.speed;
                
                // 螺旋运动（用于翻滚特效）
                if (config.spiral) {
                    const angle = time * 10;
                    const radius = 0.5 * time;
                    positions[i] += Math.cos(angle + index) * radius;
                    positions[i + 2] += Math.sin(angle + index) * radius;
                }
            }
            
            // 更新透明度
            const opacity = (1 - progress) * (this.materials.spark.opacity || 0.8);
            particles.material.opacity = Math.max(0, opacity);
            
            particles.geometry.attributes.position.needsUpdate = true;
            
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    /**
     * 根据动作触发对应特效
     */
    triggerActionEffect(actionName, position) {
        const effectMap = {
            'punch': () => this.createPunchEffect(position),
            'kick': () => this.createKickEffect(position),
            'jump': () => this.createJumpDustEffect(position),
            'roll': () => this.createRollEffect(position)
        };
        
        const effect = effectMap[actionName];
        if (effect) {
            effect();
        }
    }
    
    /**
     * 清理所有粒子
     */
    dispose() {
        this.particleGroups.forEach(particles => {
            this.scene.remove(particles);
            particles.geometry.dispose();
        });
        
        this.particleGroups.clear();
        
        // 清理材质
        Object.values(this.materials).forEach(material => {
            material.dispose();
        });
        
        console.log('🗑️ 粒子系统已清理');
    }
}
