/**
 * Three.js场景管理器
 * 负责创建和管理3D场景的基础组件：渲染器、相机、场景、光源等
 * 符合规则文件要求的标准Three.js场景架构
 */

import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

export class SceneManager {
    constructor(container) {
        this.container = container;
        
        // 核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // 渲染循环
        this.animationId = null;
        this.clock = new THREE.Clock();
        
        // 初始化场景
        this.init();
    }
    
    init() {
        // console.log('🎬 初始化Three.js场景管理器...');
        
        // 创建场景
        this.createScene();
        
        // 创建相机
        this.createCamera();
        
        // 创建渲染器
        this.createRenderer();
        
        // 创建控制器
        this.createControls();
        
        // 添加光源
        this.createLights();

        // 创建地面和环境
        this.createEnvironment();

        // 监听窗口大小变化
        this.setupEventListeners();
        
        // console.log('✅ Three.js场景初始化完成');
    }
    
    createScene() {
        this.scene = new THREE.Scene();
        
        // 设置卡通风格的背景色（天空蓝到草绿的渐变）
        this.scene.background = new THREE.Color(0x87CEEB); // 天空蓝
        
        // 添加雾效果增强卡通风格
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
    }
    
    createCamera() {
        const aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);

        // 设置相机位置，适合观察功夫狗表演
        this.camera.position.set(5, 2, 6);
        this.camera.lookAt(0, 1.2, 0); // 看向狗的中心位置
    }
    
    createRenderer() {
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true 
        });
        
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // 启用阴影
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 设置色调映射以获得更好的视觉效果
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        // 将渲染器添加到容器
        this.container.appendChild(this.renderer.domElement);
    }
    
    createControls() {
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        
        // 设置控制器参数
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        
        // 限制相机移动范围
        this.controls.minDistance = 3;
        this.controls.maxDistance = 20;
        this.controls.maxPolarAngle = Math.PI / 2; // 不能看到地面下方
        
        // 设置目标点（功夫狗的位置）
        this.controls.target.set(0, 1.2, 0);
    }
    
    createLights() {
        // 1. 环境光 - 提供基础照明
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        this.scene.add(ambientLight);
        
        // 2. 主光源 - 模拟太阳光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        
        // 设置阴影参数
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -10;
        directionalLight.shadow.camera.right = 10;
        directionalLight.shadow.camera.top = 10;
        directionalLight.shadow.camera.bottom = -10;
        
        this.scene.add(directionalLight);
        
        // 3. 补光 - 从侧面照亮，减少阴影过重
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
        fillLight.position.set(-5, 5, -5);
        this.scene.add(fillLight);
        
        // 4. 背光 - 增强轮廓
        const rimLight = new THREE.DirectionalLight(0xffffff, 0.2);
        rimLight.position.set(0, 5, -10);
        this.scene.add(rimLight);
        
        // console.log('💡 场景光源设置完成');
    }

    createEnvironment() {
        // 创建地面
        this.createGround();

        // 创建天空盒/背景元素
        this.createSkybox();

        // console.log('🌍 场景环境创建完成');
    }

    createGround() {
        // 创建地面几何体 - 使用平面几何体
        const groundGeometry = new THREE.PlaneGeometry(50, 50);

        // 创建卡通风格的地面材质
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x90EE90, // 浅绿色
            transparent: true,
            opacity: 0.8
        });

        // 创建地面网格
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2; // 旋转90度使其水平
        ground.position.y = 0; // 确保地面在 y=0 位置
        ground.receiveShadow = true; // 接收阴影

        this.scene.add(ground);

        // 添加地面网格线以增强视觉效果
        const gridHelper = new THREE.GridHelper(50, 50, 0x888888, 0xcccccc);
        gridHelper.position.y = 0.005; // 稍微抬高避免z-fighting，但保持接近地面
        this.scene.add(gridHelper);

        // console.log('🌍 地面创建完成 - 位置: y=0');
    }

    createSkybox() {
        // 创建简单的天空盒 - 使用球体几何体
        const skyGeometry = new THREE.SphereGeometry(100, 32, 32);

        // 创建天空材质 - 渐变效果
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB, // 天空蓝
            side: THREE.BackSide, // 内表面渲染
            transparent: true,
            opacity: 0.8
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);

        // 添加一些装饰性的云朵
        this.createClouds();
    }

    createClouds() {
        // 创建简单的云朵装饰
        const cloudGeometry = new THREE.SphereGeometry(3, 8, 8);
        const cloudMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.7
        });

        // 添加几朵云
        for (let i = 0; i < 5; i++) {
            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);

            // 随机位置
            cloud.position.set(
                (Math.random() - 0.5) * 80,
                15 + Math.random() * 10,
                (Math.random() - 0.5) * 80
            );

            // 随机缩放
            const scale = 0.5 + Math.random() * 0.5;
            cloud.scale.set(scale, scale * 0.6, scale);

            this.scene.add(cloud);
        }
    }
    
    setupEventListeners() {
        window.addEventListener('resize', this.onWindowResize.bind(this));
    }
    
    onWindowResize() {
        // 更新相机宽高比
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        
        // 更新渲染器大小
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }
    
    addToScene(object) {
        this.scene.add(object);
    }
    
    removeFromScene(object) {
        this.scene.remove(object);
    }

    /**
     * 渲染场景
     */
    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    // 渲染循环现在由主应用管理，这里保留方法以备后用
    startRenderLoop() {
        console.log('🎮 场景管理器渲染循环（由主应用管理）');
    }
    
    stopRenderLoop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
    
    dispose() {
        this.stopRenderLoop();
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        if (this.controls) {
            this.controls.dispose();
        }
        
        window.removeEventListener('resize', this.onWindowResize.bind(this));
    }
}
