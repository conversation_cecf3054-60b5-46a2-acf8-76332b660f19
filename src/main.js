/**
 * Three.js功夫狗3D项目主入口文件
 * 根据规则文件要求实现拟人化功夫狗的3D场景
 */

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

import { SceneManager } from './core/SceneManager.js';
import { KungFuDog } from './characters/KungFuDog.js';
import { SpeechSystem } from './systems/SpeechSystem.js';
import { AnimationController } from './systems/AnimationController.js';
import { InteractionManager } from './systems/InteractionManager.js';
import { ParticleSystem } from './effects/ParticleSystem.js';
import { AudioSystem } from './systems/AudioSystem.js';
import { BattleSystem } from './systems/BattleSystem.js';
import { AISystem } from './systems/AISystem.js';
import { BehaviorSystem } from './systems/BehaviorSystem.js';

class KungFuDogApp {
    constructor() {
        this.container = document.getElementById('canvas-container');
        this.loadingElement = document.getElementById('loading');
        
        // 核心组件
        this.sceneManager = null;
        this.kungFuDog = null;
        this.speechSystem = null;
        this.animationController = null;
        this.interactionManager = null;
        this.particleSystem = null;
        this.audioSystem = null;
        this.battleSystem = null;
        this.aiSystem = null;
        this.behaviorSystem = null;
        
        // 初始化应用
        this.init();
    }
    
    async init() {
        try {
            // console.log('🐕 开始初始化功夫狗3D项目...');
            
            // 1. 创建场景管理器
            this.sceneManager = new SceneManager(this.container);
            
            // 2. 初始化语音系统
            this.speechSystem = new SpeechSystem();

            // 3. 初始化音效系统
            this.audioSystem = new AudioSystem();

            // 4. 初始化AI系统
            this.aiSystem = new AISystem();

            // 5. 创建功夫狗角色（暂时使用占位模型）
            this.kungFuDog = new KungFuDog();
            await this.kungFuDog.init(this.sceneManager.scene);

            // 5. 将狗添加到场景
            this.sceneManager.addToScene(this.kungFuDog.model);

            // 6. 创建粒子系统
            this.particleSystem = new ParticleSystem(this.sceneManager.scene);

            // 7. 创建动画控制器
            this.animationController = new AnimationController(this.kungFuDog);

            // 8. 创建交互管理器
            this.interactionManager = new InteractionManager(
                this.kungFuDog,
                this.animationController,
                this.speechSystem,
                this.sceneManager
            );

            // 9. 初始化战斗系统
            this.battleSystem = new BattleSystem(
                this.animationController,
                this.audioSystem,
                this.speechSystem,
                this.particleSystem
            );

            // 10. 初始化行为系统
            this.behaviorSystem = new BehaviorSystem(
                this.kungFuDog,
                this.animationController,
                this.speechSystem,
                this.audioSystem
            );

            // 将系统设置到功夫狗对象上，方便访问
            this.kungFuDog.behaviorSystem = this.behaviorSystem;
            this.kungFuDog.animationController = this.animationController;

            // 11. 将系统传递给交互管理器
            this.interactionManager.setParticleSystem(this.particleSystem);
            this.interactionManager.setAudioSystem(this.audioSystem);
            this.interactionManager.setBattleSystem(this.battleSystem);
            this.interactionManager.setAISystem(this.aiSystem);
            this.interactionManager.setBehaviorSystem(this.behaviorSystem);

            // 11. 设置全局函数供HTML调用
            this.setupGlobalFunctions();

            // 12. 开始渲染循环
            this.startRenderLoop();
            
            // 隐藏加载提示
            this.loadingElement.style.display = 'none';
            
            // console.log('✅ 功夫狗3D项目初始化完成！');
            
            // 播放欢迎动画和对话
            this.playWelcomeSequence();
            
        } catch (error) {
            console.error('❌ 初始化失败:', error);
            this.loadingElement.textContent = '加载失败，请刷新页面重试';
        }
    }
    
    setupGlobalFunctions() {
        // 设置全局对象引用
        window.app = this;
        window.kungFuDog = this.kungFuDog;

        // 将函数绑定到全局作用域，供HTML按钮调用
        window.playAction = (actionName) => {
            this.animationController.playAction(actionName);
        };
        
        window.speakText = (text) => {
            this.speechSystem.speak(text, this.kungFuDog);
        };

        window.performKungfuDemo = () => {
            if (this.behaviorSystem) {
                this.behaviorSystem.performKungfuDemo();
            } else {
                this.interactionManager.performKungfuDemo();
            }
        };



        // 眨眼控制函数
        window.triggerBlink = () => {
            if (this.kungFuDog) {
                this.kungFuDog.triggerBlink();
            }
        };

        window.setBlinkInterval = (seconds) => {
            if (this.kungFuDog) {
                this.kungFuDog.setBlinkInterval(seconds * 1000);
            }
        };

        window.toggleBlink = () => {
            if (this.kungFuDog) {
                const status = this.kungFuDog.getBlinkStatus();
                this.kungFuDog.setBlinkEnabled(!status.enabled);
            }
        };

        // 模型分析函数
        window.getModelAnalysis = () => {
            if (this.kungFuDog) {
                return this.kungFuDog.getModelAnalysis();
            }
            return null;
        };

        window.getShapeKeys = () => {
            if (this.kungFuDog) {
                const shapeKeys = this.kungFuDog.getShapeKeys();
                console.log('🎭 Shape Keys列表:');
                if (shapeKeys.length > 0) {
                    shapeKeys.forEach(sk => {
                        console.log(`   ${sk.meshName}.${sk.keyName} (索引: ${sk.index})`);
                    });
                } else {
                    console.log('   没有找到Shape Keys');
                }
                return shapeKeys;
            }
            return [];
        };

        window.showAnalysis = () => {
            const panel = document.getElementById('model-analysis-panel');
            if (panel) {
                panel.style.opacity = '1';
                panel.style.display = 'block';
            } else if (this.kungFuDog && this.kungFuDog.modelAnalysis) {
                this.kungFuDog.displayAnalysisInUI(this.kungFuDog.modelAnalysis);
            }
        };

        // 测试 blink Shape Key（自动查找正确的网格）
        window.testBlink = (value = 1) => {
            if (this.kungFuDog && this.kungFuDog.model) {
                let found = false;
                this.kungFuDog.model.traverse((child) => {
                    if (child.isMesh && child.morphTargetDictionary) {
                        if (child.morphTargetDictionary['blink'] !== undefined) {
                            const index = child.morphTargetDictionary['blink'];
                            if (child.morphTargetInfluences) {
                                child.morphTargetInfluences[index] = value;
                                console.log(`✅ 设置 ${child.name}.blink = ${value} (索引: ${index})`);
                                console.log(`   当前值: ${child.morphTargetInfluences[index]}`);
                                found = true;
                                return; // 找到后退出
                            } else {
                                console.log('❌ morphTargetInfluences为空');
                            }
                        }
                    }
                });

                if (!found) {
                    console.log('❌ 未找到任何网格的blink Shape Key');
                }
                return found;
            }
            return false;
        };

        // 战斗系统相关函数
        window.toggleBattleMode = () => {
            if (this.interactionManager) {
                this.interactionManager.toggleBattleMode();
            }
        };

        window.resetBattle = () => {
            if (this.battleSystem) {
                this.battleSystem.resetBattle();
                console.log('🔄 战斗状态已重置');
            }
        };

        window.getBattleStatus = () => {
            if (this.battleSystem) {
                const status = this.battleSystem.getBattleStatus();
                console.log('⚔️ 当前战斗状态:', status);
                return status;
            }
        };

        // 状态显示更新函数
        window.updateStatusDisplay = () => {
            this.updateStatusDisplay();
        };

        window.getHealthStatus = () => {
            if (this.kungFuDog) {
                return {
                    current: this.kungFuDog.getHealth ? this.kungFuDog.getHealth() : 20,
                    max: this.kungFuDog.getMaxHealth ? this.kungFuDog.getMaxHealth() : 20,
                    isDown: this.kungFuDog.isDownState ? this.kungFuDog.isDownState() : false
                };
            }
            return null;
        };

        window.getEmotionStatus = () => {
            if (this.kungFuDog) {
                return {
                    emotions: this.kungFuDog.getEmotionalState(),
                    dominant: this.kungFuDog.getDominantEmotion()
                };
            }
            return null;
        };

        // 调试功能：显示所有可点击的网格
        window.showClickableMeshes = () => {
            if (this.kungFuDog && this.kungFuDog.model) {
                console.log('🎯 可点击的网格列表:');
                const meshes = [];

                this.kungFuDog.model.traverse((child) => {
                    if (child.isMesh) {
                        const meshInfo = {
                            name: child.name,
                            type: child.geometry.type,
                            vertices: child.geometry.attributes.position ? child.geometry.attributes.position.count : 0,
                            position: {
                                x: child.position.x.toFixed(2),
                                y: child.position.y.toFixed(2),
                                z: child.position.z.toFixed(2)
                            }
                        };
                        meshes.push(meshInfo);
                        console.log(`   📦 ${child.name}: ${child.geometry.type} (${meshInfo.vertices} 顶点)`);
                        console.log(`      位置: (${meshInfo.position.x}, ${meshInfo.position.y}, ${meshInfo.position.z})`);
                    }
                });

                console.log(`📊 总共找到 ${meshes.length} 个网格`);
                return meshes;
            }
            return [];
        };

        // 调试功能：测试特定网格的点击
        window.testMeshClick = (meshName) => {
            if (this.interactionManager && this.kungFuDog && this.kungFuDog.model) {
                let targetMesh = null;

                this.kungFuDog.model.traverse((child) => {
                    if (child.isMesh && child.name === meshName) {
                        targetMesh = child;
                    }
                });

                if (targetMesh) {
                    // 模拟点击该网格
                    const fakeIntersection = {
                        object: targetMesh,
                        point: targetMesh.position.clone()
                    };

                    console.log(`🧪 模拟点击网格: ${meshName}`);
                    this.interactionManager.handleDogClick(fakeIntersection);
                } else {
                    console.log(`❌ 未找到网格: ${meshName}`);
                }
            }
        };

        // Armature 碰撞体控制功能
        window.toggleArmatureCollision = (visible = null) => {
            if (this.kungFuDog) {
                const isVisible = this.kungFuDog.toggleArmatureCollision(visible);
                console.log(`📦 Armature 碰撞体可视化: ${isVisible ? '开启' : '关闭'}`);
                return isVisible;
            }
            return false;
        };





        // Box3Helper控制功能
        window.toggleBox3Helpers = (visible = null) => {
            if (this.kungFuDog) {
                const isVisible = this.kungFuDog.toggleBox3Helpers(visible);
                console.log(`📐 Box3Helper可视化: ${isVisible ? '开启' : '关闭'}`);
                return isVisible;
            }
            return false;
        };

        window.testArmatureCollision = () => {
            if (this.interactionManager && this.kungFuDog) {
                const armatureCollisions = this.kungFuDog.getArmatureCollision();

                if (armatureCollisions && armatureCollisions.length > 0) {
                    // 模拟点击第一个 Armature 碰撞体
                    const firstCollision = armatureCollisions[0];
                    const fakeIntersection = {
                        object: firstCollision,
                        point: firstCollision.position.clone()
                    };

                    console.log(`🧪 模拟点击 Armature 碰撞体 (共${armatureCollisions.length}个碰撞盒，测试第1个)`);
                    this.interactionManager.handleDogClick(fakeIntersection);
                } else {
                    console.log('❌ 未找到 Armature 碰撞体');
                }
            }
        };

        // 调试功能：获取 Armature 碰撞体信息
        window.getArmatureCollisionInfo = () => {
            if (this.kungFuDog) {
                const collision = this.kungFuDog.armatureCollision;
                const info = {
                    hasArmature: !!collision.armature,
                    collisionMeshCount: collision.collisionMeshes.length,
                    helperCount: collision.helpers.length,
                    visible: collision.visible,
                    armatureName: collision.armature ? collision.armature.name : 'N/A'
                };
                console.log('📊 Armature 碰撞体信息:', info);
                return info;
            }
            console.log('❌ 功夫狗未初始化');
            return null;
        };

        // 调试功能：打印所有动画
        window.printAllAnimations = () => {
            if (this.animationController) {
                this.animationController.printAllAnimations();
                return true;
            }
            console.log('❌ 动画控制器未初始化');
            return false;
        };

        // 调试功能：获取动画列表
        window.getAnimationList = () => {
            if (this.animationController && this.animationController.actions) {
                const animations = Array.from(this.animationController.actions.keys()).sort();
                console.log('📋 动画名称列表:', animations);
                return animations;
            }
            console.log('❌ 动画控制器未初始化');
            return [];
        };

        // 调试功能：直接测试 Hurt 动画
        window.testHurtAnimation = () => {
            if (this.animationController) {
                console.log('🧪 直接测试 Hurt 动画...');
                this.animationController.playAction('Hurt');
                return true;
            }
            console.log('❌ 动画控制器未初始化');
            return false;
        };

        // 调试功能：测试头部点击
        window.testHeadClick = () => {
            if (this.interactionManager) {
                console.log('🧪 模拟头部点击...');
                this.interactionManager.handleBodyPartInteraction('head');
                return true;
            }
            console.log('❌ 交互管理器未初始化');
            return false;
        };

        // 调试功能：直接测试 Left leg hurt 动画
        window.testLeftLegHurtAnimation = () => {
            if (this.animationController) {
                console.log('🧪 直接测试 Left leg hurt 动画...');
                this.animationController.playAction('Left leg hurt');
                return true;
            }
            console.log('❌ 动画控制器未初始化');
            return false;
        };

        // 调试功能：重新创建动画映射
        window.recreateAnimationMapping = () => {
            if (this.animationController) {
                console.log('🔄 重新创建动画映射...');
                this.animationController.createAnimationMapping();
                console.log('✅ 动画映射重新创建完成');
                return true;
            }
            console.log('❌ 动画控制器未初始化');
            return false;
        };

        // 调试功能：检查动画映射状态
        window.checkAnimationMapping = () => {
            if (this.animationController && this.animationController.animationMapping) {
                console.log('🔍 当前动画映射状态:');
                console.log('─'.repeat(50));

                const mappings = this.animationController.animationMapping;
                if (mappings.size === 0) {
                    console.log('❌ 没有任何动画映射');
                    return;
                }

                mappings.forEach((realName, genericName) => {
                    const exists = this.animationController.actions.has(realName);
                    const status = exists ? '✅' : '❌';
                    console.log(`${status} ${genericName} → ${realName}`);
                });

                console.log('─'.repeat(50));
                console.log(`📊 总计: ${mappings.size} 个映射`);

                // 特别检查 Hurt 映射
                if (mappings.has('Hurt')) {
                    const hurtMapping = mappings.get('Hurt');
                    console.log(`🎯 Hurt 映射: Hurt → ${hurtMapping}`);
                } else {
                    console.log('⚠️ 没有找到 Hurt 的映射');
                }

                return mappings;
            }
            console.log('❌ 动画控制器或映射未初始化');
            return null;
        };

        // 调试功能：直接测试带空格的 Hurt 动画
        window.testHurtWithSpace = () => {
            if (this.animationController) {
                console.log('🧪 直接测试带空格的 Hurt 动画...');
                this.animationController.playAction('Hurt ');  // 注意这里有空格
                return true;
            }
            console.log('❌ 动画控制器未初始化');
            return false;
        };



        // 调试功能：修复动画高度
        window.fixAnimationHeights = () => {
            if (this.animationController) {
                return this.animationController.fixAnimationHeights();
            }
            console.log('❌ 动画控制器未初始化');
            return false;
        };

        // AI系统调试功能
        window.testAI = async (message) => {
            if (this.aiSystem) {
                try {
                    const reply = await this.aiSystem.getKungFuDogReply(message || '你好');
                    console.log('🤖 AI回复:', reply);
                    // 让功夫狗说出AI的回复
                    if (this.speechSystem) {
                        this.speechSystem.speak(reply, this.kungFuDog);
                    }
                    return reply;
                } catch (error) {
                    console.error('❌ AI测试失败:', error);
                    return null;
                }
            }
            console.log('❌ AI系统未初始化');
            return null;
        };

        window.checkAIConnection = async () => {
            if (this.aiSystem) {
                const isConnected = await this.aiSystem.checkConnection();
                console.log(`🔗 AI连接状态: ${isConnected ? '正常' : '异常'}`);
                return isConnected;
            }
            console.log('❌ AI系统未初始化');
            return false;
        };

        window.clearAIHistory = () => {
            if (this.aiSystem) {
                this.aiSystem.clearHistory();
                console.log('🧹 AI对话历史已清空');
                return true;
            }
            console.log('❌ AI系统未初始化');
            return false;
        };

        // 行为系统调试功能
        window.triggerBehavior = (behaviorName) => {
            if (this.behaviorSystem) {
                return this.behaviorSystem.triggerBehavior(behaviorName, true);
            }
            console.log('❌ 行为系统未初始化');
            return false;
        };

        window.getBehaviorStatus = () => {
            if (this.behaviorSystem) {
                const status = this.behaviorSystem.getSystemStatus();
                console.log('🎭 行为系统状态:', status);
                return status;
            }
            console.log('❌ 行为系统未初始化');
            return null;
        };

        window.setEmotionalState = (emotion, value) => {
            if (this.kungFuDog && this.kungFuDog.setEmotionalState) {
                return this.kungFuDog.setEmotionalState(emotion, value);
            } else if (this.behaviorSystem) {
                return this.behaviorSystem.setEmotionalState(emotion, value);
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.toggleAutoBehavior = () => {
            if (this.behaviorSystem) {
                const currentState = this.behaviorSystem.autoBehavior.enabled;
                this.behaviorSystem.setAutoBehaviorEnabled(!currentState);
                console.log(`🤖 自动行为: ${!currentState ? '启用' : '禁用'}`);
                return !currentState;
            }
            console.log('❌ 行为系统未初始化');
            return false;
        };

        // 身体部位交互测试
        window.testBodyPartClick = (bodyPart) => {
            if (this.interactionManager) {
                console.log(`🧪 模拟点击身体部位: ${bodyPart}`);
                this.interactionManager.handleBodyPartInteraction(bodyPart);
                return true;
            }
            console.log('❌ 交互管理器未初始化');
            return false;
        };

        window.testAllBodyParts = () => {
            const bodyParts = ['head', 'body', 'legs', 'feet'];
            bodyParts.forEach((part, index) => {
                setTimeout(() => {
                    window.testBodyPartClick(part);
                }, index * 3000); // 每3秒测试一个部位
            });
        };

        // 生命值系统测试函数
        window.getHealthStatus = () => {
            if (this.kungFuDog && this.kungFuDog.getHealthStatus) {
                const status = this.kungFuDog.getHealthStatus();
                console.log('❤️ 生命值状态:', status);
                return status;
            }
            console.log('❌ 生命值系统未初始化');
            return null;
        };

        window.takeDamage = (amount = 1) => {
            if (this.kungFuDog && this.kungFuDog.takeDamage) {
                const damage = this.kungFuDog.takeDamage(amount);
                console.log(`💔 造成伤害: ${damage}`);
                return damage;
            }
            console.log('❌ 生命值系统未初始化');
            return 0;
        };

        window.heal = (amount = 1) => {
            if (this.kungFuDog && this.kungFuDog.heal) {
                const healed = this.kungFuDog.heal(amount);
                console.log(`💚 恢复生命值: ${healed}`);
                return healed;
            }
            console.log('❌ 生命值系统未初始化');
            return 0;
        };

        window.fullHeal = () => {
            if (this.kungFuDog && this.kungFuDog.fullHeal) {
                this.kungFuDog.fullHeal();
                console.log('✨ 完全恢复！');
                return true;
            }
            console.log('❌ 生命值系统未初始化');
            return false;
        };

        window.getEmotionalState = () => {
            if (this.kungFuDog && this.kungFuDog.getEmotionalState) {
                const emotions = this.kungFuDog.getEmotionalState();
                console.log('😊 情绪状态:', emotions);
                return emotions;
            }
            console.log('❌ 情绪系统未初始化');
            return null;
        };

        // 新增情绪测试函数
        window.makeAngry = (value = 0.8) => {
            if (this.kungFuDog && this.kungFuDog.setEmotionalState) {
                this.kungFuDog.setEmotionalState('anger', value);
                console.log('😡 功夫狗变愤怒了！');
                return true;
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.makeFearful = (value = 0.8) => {
            if (this.kungFuDog && this.kungFuDog.setEmotionalState) {
                this.kungFuDog.setEmotionalState('fear', value);
                console.log('😨 功夫狗变恐惧了！');
                return true;
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.makeSnarky = (value = 0.8) => {
            if (this.kungFuDog && this.kungFuDog.setEmotionalState) {
                this.kungFuDog.setEmotionalState('snark', value);
                console.log('😏 功夫狗变贱气了！');
                return true;
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.makePlayful = (value = 1.0) => {
            if (this.kungFuDog && this.kungFuDog.setEmotionalState) {
                this.kungFuDog.setEmotionalState('playfulness', value);
                console.log('🎮 功夫狗变得很有玩心！');
                return true;
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.testEmotionInteractions = () => {
            if (this.kungFuDog && this.kungFuDog.updateEmotionalState) {
                console.log('🧪 测试情绪相互影响...');

                // 测试不同行为类型对情绪的影响
                const behaviors = ['aggressive', 'playful', 'snarky', 'fearful'];
                behaviors.forEach((behavior, index) => {
                    setTimeout(() => {
                        console.log(`🎭 触发 ${behavior} 行为`);
                        this.kungFuDog.updateEmotionalState(behavior);
                    }, index * 2000);
                });

                return true;
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.resetEmotions = () => {
            if (this.kungFuDog && this.kungFuDog.setEmotionalState) {
                const defaultEmotions = {
                    happiness: 0.7,
                    energy: 0.8,
                    alertness: 0.6,
                    friendliness: 0.9,
                    anger: 0.1,
                    fear: 0.2,
                    snark: 0.3,
                    playfulness: 0.8
                };

                for (const [emotion, value] of Object.entries(defaultEmotions)) {
                    this.kungFuDog.setEmotionalState(emotion, value);
                }

                console.log('🔄 情绪状态已重置为默认值');
                return true;
            }
            console.log('❌ 情绪系统未初始化');
            return false;
        };

        window.getAIHistory = () => {
            if (this.aiSystem) {
                const history = this.aiSystem.getHistory();
                console.log('📚 AI对话历史:', history);
                return history;
            }
            console.log('❌ AI系统未初始化');
            return [];
        };

        // AI对话界面控制
        window.toggleAIChat = () => {
            const panel = document.getElementById('ai-chat-panel');
            if (panel) {
                const isVisible = panel.style.display !== 'none';
                panel.style.display = isVisible ? 'none' : 'block';

                if (!isVisible) {
                    // 显示面板时，聚焦输入框
                    const input = document.getElementById('ai-chat-input');
                    if (input) {
                        setTimeout(() => input.focus(), 100);
                    }
                }
            }
        };

        window.sendAIMessage = async () => {
            const input = document.getElementById('ai-chat-input');
            const history = document.getElementById('ai-chat-history');

            if (!input || !history || !this.aiSystem) return;

            const message = input.value.trim();
            if (!message) return;

            // 清空输入框
            input.value = '';

            // 添加用户消息到历史
            this.addChatMessage('用户', message, '#4CAF50');

            try {
                // 显示加载状态
                this.addChatMessage('功夫狗', '思考中...', '#FFA500');

                // 获取AI回复（包含情绪状态）
                const result = await this.aiSystem.getKungFuDogReply(message, this.kungFuDog);
                const { reply, emotionEvent } = result;

                // 移除加载消息
                const messages = history.children;
                if (messages.length > 0) {
                    history.removeChild(messages[messages.length - 1]);
                }

                // 添加AI回复
                this.addChatMessage('功夫狗', reply, '#FF6B35');

                // 处理情绪事件
                if (emotionEvent && this.kungFuDog && this.kungFuDog.updateEmotionalState) {
                    console.log(`🎭 AI触发情绪事件: ${emotionEvent}`);
                    this.kungFuDog.updateEmotionalState(emotionEvent);

                    // 添加情绪事件提示
                    const emotionNames = {
                        social: '🤝 社交互动',
                        performance: '🎭 表演展示',
                        active: '⚡ 活跃运动',
                        defensive: '🛡️ 防御警戒',
                        aggressive: '⚔️ 攻击冲动',
                        playful: '🎮 玩乐嬉戏',
                        snarky: '😏 调皮贱气',
                        fearful: '😨 恐惧紧张'
                    };

                    const emotionName = emotionNames[emotionEvent] || `🎭 ${emotionEvent}`;
                    this.addChatMessage('系统', `情绪变化: ${emotionName}`, '#9C27B0');
                }

                // 让功夫狗说出回复
                if (this.speechSystem) {
                    this.speechSystem.speak(reply, this.kungFuDog);
                }

                // 根据回复建议动作
                const action = await this.aiSystem.getActionSuggestion(message);
                if (action && this.animationController) {
                    setTimeout(() => {
                        this.animationController.playAction(action);
                    }, 1000);
                }

            } catch (error) {
                console.error('❌ AI对话失败:', error);
                // 移除加载消息
                const messages = history.children;
                if (messages.length > 0) {
                    history.removeChild(messages[messages.length - 1]);
                }
                this.addChatMessage('系统', '对话失败，请稍后重试', '#FF4444');
            }
        };

        window.clearAIChatHistory = () => {
            const history = document.getElementById('ai-chat-history');
            if (history) {
                history.innerHTML = '';
            }
            if (this.aiSystem) {
                this.aiSystem.clearHistory();
            }
        };

        // 设置AI对话输入框的回车事件
        setTimeout(() => {
            const input = document.getElementById('ai-chat-input');
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        window.sendAIMessage();
                    }
                });
            }
        }, 1000);
    }

    /**
     * 添加聊天消息到AI对话历史
     * @param {string} sender - 发送者名称
     * @param {string} message - 消息内容
     * @param {string} color - 消息颜色
     */
    addChatMessage(sender, message, color) {
        const history = document.getElementById('ai-chat-history');
        if (!history) return;

        const messageDiv = document.createElement('div');
        messageDiv.style.marginBottom = '8px';
        messageDiv.style.padding = '5px';
        messageDiv.style.borderRadius = '5px';
        messageDiv.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';

        const time = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div style="color: ${color}; font-weight: bold; font-size: 11px;">
                ${sender} ${time}
            </div>
            <div style="margin-top: 2px; font-size: 12px; line-height: 1.4;">
                ${message}
            </div>
        `;

        history.appendChild(messageDiv);
        history.scrollTop = history.scrollHeight;
    }






    startRenderLoop() {
        let lastTime = performance.now();

        const animate = (currentTime) => {
            requestAnimationFrame(animate);

            const deltaTime = currentTime - lastTime;
            lastTime = currentTime;

            // 更新角色系统（包括动画、情绪、生命值）
            if (this.kungFuDog && this.kungFuDog.update) {
                this.kungFuDog.update(deltaTime);
            } else if (this.kungFuDog && this.kungFuDog.mixer) {
                // 回退到只更新动画混合器
                this.kungFuDog.mixer.update(deltaTime / 1000);
            }

            // 更新粒子系统
            if (this.particleSystem) {
                this.particleSystem.update();
            }

            // 更新状态显示（每秒更新一次）
            if (!this.lastStatusUpdate || currentTime - this.lastStatusUpdate > 1000) {
                this.updateStatusDisplay();
                this.lastStatusUpdate = currentTime;
            }

            // 渲染场景
            this.sceneManager.render();
        };

        animate(performance.now());
    }

    playWelcomeSequence() {
        // 延迟播放欢迎动画，确保模型已加载
        setTimeout(() => {
            this.animationController.playAction('stand');
            setTimeout(() => {
                this.speechSystem.speak('大家好，我是功夫狗！准备好看我的功夫了吗？', this.kungFuDog);
            }, 500);
        }, 1000);
    }

    /**
     * 更新状态显示面板
     */
    updateStatusDisplay() {
        if (!this.kungFuDog) return;

        try {
            // 更新生命值显示
            this.updateHealthDisplay();

            // 更新情绪显示
            this.updateEmotionDisplay();
        } catch (error) {
            console.warn('⚠️ 状态显示更新失败:', error);
        }
    }

    /**
     * 更新生命值显示
     */
    updateHealthDisplay() {
        const healthText = document.getElementById('health-text');
        const healthStatus = document.getElementById('health-status');
        const healthFill = document.getElementById('health-fill');

        if (healthText && healthStatus && healthFill) {
            const current = this.kungFuDog.getHealth ? this.kungFuDog.getHealth() : 20;
            const max = this.kungFuDog.getMaxHealth ? this.kungFuDog.getMaxHealth() : 20;
            const isDown = this.kungFuDog.isDownState ? this.kungFuDog.isDownState() : false;

            // 更新文本
            healthText.textContent = `${current}/${max}`;

            // 更新状态
            if (isDown) {
                healthStatus.textContent = '倒下';
                healthStatus.style.color = '#FF4444';
            } else if (current === max) {
                healthStatus.textContent = '完美';
                healthStatus.style.color = '#2ECC71';
            } else if (current > max * 0.6) {
                healthStatus.textContent = '良好';
                healthStatus.style.color = '#FFD700';
            } else {
                healthStatus.textContent = '受伤';
                healthStatus.style.color = '#FF6B35';
            }

            // 更新生命值条
            const percentage = (current / max) * 100;
            healthFill.style.width = `${percentage}%`;

            // 根据生命值调整颜色
            if (percentage > 60) {
                healthFill.style.background = 'linear-gradient(90deg, #2ECC71, #27AE60)';
            } else if (percentage > 30) {
                healthFill.style.background = 'linear-gradient(90deg, #FFD700, #F39C12)';
            } else {
                healthFill.style.background = 'linear-gradient(90deg, #FF4444, #E74C3C)';
            }
        }
    }

    /**
     * 更新情绪显示
     */
    updateEmotionDisplay() {
        const emotions = this.kungFuDog.getEmotionalState();
        const dominantEmotion = this.kungFuDog.getDominantEmotion();

        if (!emotions) return;

        // 更新各个情绪值
        const emotionNames = ['happiness', 'energy', 'alertness', 'friendliness', 'anger', 'fear', 'snark', 'playfulness'];

        emotionNames.forEach(emotion => {
            const element = document.getElementById(`emotion-${emotion}`);
            if (element && emotions[emotion] !== undefined) {
                element.textContent = emotions[emotion].toFixed(2);

                // 根据情绪值调整颜色
                const value = emotions[emotion];
                if (value > 0.7) {
                    element.style.color = '#2ECC71';
                } else if (value > 0.4) {
                    element.style.color = '#FFD700';
                } else {
                    element.style.color = '#FF6B35';
                }
            }
        });

        // 更新主导情绪
        const dominantElement = document.getElementById('dominant-emotion');
        if (dominantElement && dominantEmotion) {
            const emotionLabels = {
                happiness: '😊 快乐',
                energy: '⚡ 精力',
                alertness: '👁️ 警觉',
                friendliness: '🤝 友好',
                anger: '😡 愤怒',
                fear: '😨 恐惧',
                snark: '😏 贱气',
                playfulness: '🎮 玩心'
            };

            dominantElement.textContent = emotionLabels[dominantEmotion] || dominantEmotion;

            // 根据主导情绪调整背景色
            const emotionColors = {
                happiness: '#FFD700',
                energy: '#FF6B35',
                alertness: '#4ECDC4',
                friendliness: '#45B7D1',
                anger: '#FF4444',
                fear: '#9B59B6',
                snark: '#E67E22',
                playfulness: '#2ECC71'
            };

            const color = emotionColors[dominantEmotion] || '#FFD700';
            dominantElement.parentElement.style.background = `rgba(${this.hexToRgb(color)}, 0.2)`;
            dominantElement.parentElement.style.borderColor = color;
        }
    }

    /**
     * 将十六进制颜色转换为RGB
     */
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ?
            `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
            '255, 215, 0';
    }
}

// 启动应用
const app = new KungFuDogApp();
