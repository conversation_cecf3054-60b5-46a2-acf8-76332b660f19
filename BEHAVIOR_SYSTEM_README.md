# 🎭 BehaviorSystem 行为系统文档

## 概述

BehaviorSystem 是从 InteractionManager 中分离出来的独立行为管理系统，负责管理功夫狗的自动行为、情绪状态和行为逻辑。

## ✅ 已完成的功能

### 1. 核心功能
- ✅ **自动行为系统** - 智能的自动行为触发机制
- ✅ **情绪状态管理** - 四维情绪模型（快乐、精力、警觉、友好）
- ✅ **行为队列管理** - 行为排队和顺序执行
- ✅ **权重选择系统** - 基于情绪状态的智能行为选择
- ✅ **冷却时间管理** - 防止行为过度重复
- ✅ **行为历史记录** - 避免短期内重复相同行为

### 2. 预定义行为
- ✅ **idle** - 待机行为
- ✅ **greeting** - 问候行为  
- ✅ **showOff** - 炫技行为
- ✅ **contemplation** - 思考行为
- ✅ **energetic** - 活跃行为
- ✅ **alert** - 警觉行为
- ✅ **kungfuDemo** - 功夫表演序列

### 3. 集成功能
- ✅ **音效系统集成** - 修复了音效映射问题
- ✅ **动画系统集成** - 完整的动画播放支持
- ✅ **语音系统集成** - 同步语音和动作
- ✅ **交互系统集成** - 用户交互记录和响应

## 🔧 修复的问题

### 1. 音效映射错误
**问题**: 原代码使用了不存在的音效名称（'whoosh', 'impact', 'block'）
**修复**: 
```javascript
// 修复前
const soundMap = {
    'performance': 'whoosh',  // ❌ 不存在
    'active': 'impact',       // ❌ 不存在
    'defensive': 'block'      // ❌ 不存在
};

// 修复后
const soundMap = {
    'performance': 'kick',    // ✅ 存在
    'active': 'jump',         // ✅ 存在
    'defensive': 'punch',     // ✅ 存在
    'social': 'bow'           // ✅ 新增
};
```

### 2. 内存泄漏问题
**问题**: 使用递归 setTimeout 可能导致内存泄漏
**修复**: 
```javascript
// 修复前
const checkBehavior = () => {
    // ... 逻辑
    setTimeout(checkBehavior, 1000); // ❌ 递归调用
};

// 修复后
this.autoBehavior.intervalId = setInterval(checkBehavior, 1000); // ✅ 使用 setInterval
// 添加清理机制
stopAutoBehavior() {
    if (this.autoBehavior.intervalId) {
        clearInterval(this.autoBehavior.intervalId);
    }
}
```

### 3. 缺失功能补充
**添加**: 
- ✅ `performKungfuDemo()` - 完整的功夫表演序列
- ✅ `stopAutoBehavior()` - 停止自动行为的清理方法
- ✅ 用户交互记录机制

## 🚀 使用方法

### 1. 基本初始化
```javascript
// 在 main.js 中
import { BehaviorSystem } from './systems/BehaviorSystem.js';

// 初始化
this.behaviorSystem = new BehaviorSystem(
    this.kungFuDog,           // 角色对象
    this.animationController, // 动画控制器
    this.speechSystem,        // 语音系统
    this.audioSystem          // 音效系统
);
```

### 2. 手动触发行为
```javascript
// 触发特定行为
behaviorSystem.triggerBehavior('greeting');

// 强制触发（忽略冷却时间）
behaviorSystem.triggerBehavior('showOff', true);

// 触发随机行为
behaviorSystem.triggerRandomBehavior();
```

### 3. 情绪状态控制
```javascript
// 设置情绪状态
behaviorSystem.setEmotionalState('happiness', 0.9);
behaviorSystem.setEmotionalState('energy', 0.5);

// 获取当前情绪状态
const emotions = behaviorSystem.getEmotionalState();
console.log(emotions); // { happiness: 0.9, energy: 0.5, ... }
```

### 4. 自动行为控制
```javascript
// 启用/禁用自动行为
behaviorSystem.setAutoBehaviorEnabled(true);
behaviorSystem.setAutoBehaviorEnabled(false);

// 记录用户交互（重置idle计时器）
behaviorSystem.recordInteraction();
```

### 5. 系统状态查询
```javascript
// 获取完整系统状态
const status = behaviorSystem.getSystemStatus();
console.log(status);
/*
{
    currentBehavior: 'greeting',
    isExecuting: true,
    queueLength: 2,
    emotionalState: { happiness: 0.7, ... },
    autoBehaviorEnabled: true,
    behaviorHistory: ['idle', 'greeting']
}
*/
```

## 🎮 全局调试函数

在浏览器控制台中可以使用以下函数：

```javascript
// 行为控制
triggerBehavior('greeting');        // 触发指定行为
performKungfuDemo();               // 功夫表演
toggleAutoBehavior();              // 切换自动行为

// 情绪控制
setEmotionalState('happiness', 0.8); // 设置情绪状态

// 状态查询
getBehaviorStatus();               // 获取行为系统状态
```

## 🧪 测试页面

访问 `http://localhost:3005/behavior-test.html` 可以使用专门的测试界面：

- **行为触发测试** - 测试各种预定义行为
- **情绪状态控制** - 实时调整情绪参数
- **系统状态显示** - 查看实时系统状态
- **3D场景集成** - 在iframe中显示实际效果

## 📊 行为权重系统

行为选择基于以下权重计算：

```javascript
// 基础权重 × 情绪调整系数
switch (behavior.type) {
    case 'social':      weight *= friendliness;  // 社交行为受友好度影响
    case 'active':      weight *= energy;        // 活跃行为受精力影响  
    case 'performance': weight *= happiness;     // 表演行为受快乐度影响
    case 'defensive':   weight *= alertness;     // 防御行为受警觉性影响
}
```

## 🔄 与原系统的集成

### InteractionManager 修改
- ✅ 移除了旧的 `autoActions` 配置
- ✅ 移除了 `startAutoActions()` 方法
- ✅ 添加了 `setBehaviorSystem()` 方法
- ✅ 在用户交互时调用 `behaviorSystem.recordInteraction()`

### Main.js 集成
- ✅ 导入 BehaviorSystem
- ✅ 初始化 behaviorSystem 实例
- ✅ 传递给 InteractionManager
- ✅ 添加全局调试函数

## 🎯 使用建议

1. **自动行为间隔**: 默认8秒，可根据需要调整
2. **情绪衰减**: 情绪会自然回归到默认值，模拟真实情绪变化
3. **行为历史**: 最多记录5个历史行为，避免短期重复
4. **冷却时间**: 每个行为都有独立的冷却时间，防止过度触发

## 🚧 未来扩展

可以考虑添加的功能：
- 更复杂的行为链和条件触发
- 基于时间的行为模式（如早晚不同行为）
- 学习用户偏好的自适应系统
- 更丰富的情绪表达和动画

---

**BehaviorSystem 现在已经完全集成并可以正常使用！** 🎉
