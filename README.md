# 🥋 Three.js 功夫狗 3D 项目

一个基于 Three.js 的交互式 3D 功夫狗项目，具备完整的角色系统、战斗机制和智能行为。

## 🎯 项目特色

- **3D 角色模型**：加载真实的 GLTF 狗模型，支持骨骼动画和 Shape Keys 面部表情
- **智能对话系统**：文本气泡显示，同步嘴型动画，拟人化对话
- **情绪系统**：四维情绪模型（快乐、精力、警觉、友好），影响行为选择
- **生命值系统**：完整的生命值管理，自动恢复机制，倒下与复活
- **战斗系统**：点击攻击、格挡反击、随机判定、战斗统计、UI 显示
- **交互控制**：键盘、鼠标、按钮多种交互方式，碰撞检测
- **视觉特效**：粒子系统、音效系统、光影效果、眨眼动画
- **自动行为**：AI 自动执行动作和对话，情绪驱动的行为权重

## 🎮 操作说明
### 鼠标控制
- **点击功夫狗**: 根据点击部位触发不同动作
- **拖拽**: 旋转视角
- **滚轮**: 缩放视图
## 🏗️ 项目结构

```
src/
├── main.js                     # 主应用入口，全局函数定义
├── core/
│   └── SceneManager.js         # Three.js 场景管理器
├── characters/
│   └── KungFuDog.js           # 功夫狗角色类（集成情绪和生命值系统）
├── systems/
│   ├── AnimationController.js  # 动画控制器，14种动画管理
│   ├── SpeechSystem.js         # 说话系统，嘴型动画同步
│   ├── InteractionManager.js   # 交互管理器，碰撞检测
│   ├── AudioSystem.js          # 音效系统，程序化音效生成
│   ├── BattleSystem.js         # 战斗系统，攻击判定和UI
│   ├── BehaviorSystem.js       # 行为系统，AI自动行为
│   └── AISystem.js             # AI对话系统
└── effects/
    └── ParticleSystem.js       # 粒子特效系统
```

## 🚀 快速开始

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **访问项目**
   打开浏览器访问 `http://localhost:3005`

## 🎨 技术实现

### 核心技术栈
- **Three.js**: 3D 图形渲染
- **Vite**: 构建工具
- **GLTF Loader**: 3D 模型加载
- **Web Audio API**: 程序化音效

### 关键功能

#### 1. 3D 场景架构
- 标准 Three.js 场景设置
- 多层光照系统（环境光、主光源、补光、背光）
- 卡通风格地面和天空盒
- 阴影和雾效

#### 2. 角色系统（KungFuDog 类）
- **动画系统**：14种动画，智能映射，平滑切换
- **情绪系统**：四维情绪模型（happiness, energy, alertness, friendliness）
- **生命值系统**：3点生命值，自动恢复，倒下复活机制
- **面部表情**：Shape Keys 控制，眨眼动画，嘴型同步
- **碰撞检测**：Armature 包围盒系统

#### 3. 对话系统
- 打字机效果文本显示
- Shape Keys 嘴型动画同步
- 预设对话库（greeting, kungfu, praise, wisdom）
- 气泡样式界面

#### 4. 战斗系统
- **攻击机制**：40%格挡概率，60%受击概率
- **反击系统**：格挡后50%概率反击
- **生命值管理**：伤害计算，自动恢复
- **战斗UI**：实时显示生命值、状态、统计
- **情绪影响**：受伤影响情绪状态

#### 5. 行为系统
- **AI自动行为**：基于情绪权重的行为选择
- **行为类型**：greeting, energetic, idle, social, active, basic
- **情绪衰减**：自然回归到默认值
- **行为冷却**：防止重复执行

#### 6. 交互系统
- 多输入方式支持（键盘、鼠标、按钮）
- 射线检测和碰撞体点击
- 动作冷却机制
- 战斗模式切换

#### 7. 特效系统
- 粒子特效（出拳、踢腿、跳跃等）
- 程序化音效生成
- 视觉反馈增强

## 🎭 动作列表

### 可用动画（14种）
1. **"stand"** - 站立待机（13.97秒，循环）
2. **"jump"** - 跳跃动作（2.70秒，单次）
3. **"kick"** - 踢腿攻击（1.63秒，单次）
4. **"Block "** - 格挡防御（2.80秒，单次）
5. **"Hurt "** - 受伤动作（1.80秒，单次）
6. **"Left leg hurt "** - 左腿受伤（6.07秒，单次）
7. **"Arm Stretching"** - 伸展动作（8.90秒，循环）
8. **"taunt"** - 挑衅动作（7.03秒，循环）
9. **"Twist Dance"** - 扭转舞蹈（1.03秒，循环）
10. **"big kick"** - 大踢腿（3.73秒，循环）
11. **"Hurricane Kick"** - 旋风踢（1.87秒，循环）
12. **"Talking "** - 说话动作（3.80秒，循环）
13. **"Talking Two hands "** - 双手说话（3.97秒，循环）
14. **"Dancing chacha"** - 恰恰舞（9.47秒，循环）

### 动画映射
- **stand** → "stand"
- **jump** → "jump"
- **kick** → "kick"
- **Block** → "Block "
- **Hurt** → "Hurt "
- **Left leg hurt** → "Left leg hurt "

### 特殊功能
- **功夫表演**: 完整的动作序列展示
- **组合技**: 连续动作组合
- **自动眨眼**: 随机间隔眨眼动画

## 💬 对话系统

### 对话类别
- **greeting**: 打招呼用语
- **kungfu**: 功夫展示用语
- **praise**: 自夸用语
- **wisdom**: 武德格言
- **battle**: 战斗相关用语
- **hurt**: 受伤时用语
- **block**: 格挡时用语
- **counter**: 反击时用语

### 示例对话
- "大家好，我是功夫狗！准备好看我的功夫了吗？"
- "看我功夫如何！"
- "武功再高，也要讲武德！"
- "功夫不是用来欺负人的！"
- "战斗模式开启！来攻击我试试！"
- "我的防御可是很强的！"
- "反击时间到！汪！"
- "汪呜..."

### Shape Keys 嘴型动画
- 使用 "closed" 和 "Key 9" Shape Keys
- 说话时自动播放嘴型动画
- 说话结束后自动停止

## 🔧 配置说明

### 动画配置
- 动画切换淡入淡出时间：300ms
- 交互冷却时间：1000ms
- 自动行为间隔：10秒
- 眨眼间隔：2-8秒随机

### 情绪系统配置
- **默认值**: happiness: 0.7, energy: 0.8, alertness: 0.6, friendliness: 0.9
- **衰减率**: 0.01/帧
- **影响强度**: 0.1

### 生命值系统配置
- **最大生命值**: 3
- **自动恢复延迟**: 5秒
- **恢复速率**: 0.1/秒

### 战斗系统配置
- **格挡概率**: 40%
- **受击概率**: 60%
- **反击概率**: 50%（格挡后）
- **战斗冷却**: 1秒

### 视觉配置
- 目标模型高度：2.5 单位
- 相机距离：3-20 单位
- 粒子生命周期：800-1500ms

## 🎵 音效系统

项目使用 Web Audio API 生成程序化音效：
- **出拳音效**: 低频冲击声
- **踢腿音效**: 高频冲击声
- **跳跃音效**: 上升音调
- **翻滚音效**: 多层混合音
- **脚步声**: 短促节拍音
- **格挡音效**: 金属碰撞声
- **受伤音效**: 低沉撞击声
- **反击音效**: 快速冲击声

## 🌟 项目亮点

1. **完全符合规则文件要求**：严格按照 Three.js 功夫狗项目规范实现
2. **面向对象架构**：角色类集成情绪和生命值系统，符合OOP原则
3. **智能动画映射**：自动匹配 GLTF 动画到通用动作名
4. **情绪驱动行为**：基于四维情绪模型的智能行为选择
5. **完整战斗系统**：格挡、反击、生命值管理、UI显示
6. **自动化管理**：情绪衰减、生命值恢复、眨眼动画
7. **丰富的交互方式**：支持多种输入方式和反馈
8. **视听一体化**：视觉特效与音效完美结合
9. **Shape Keys 面部动画**：嘴型同步、眨眼表情
10. **AI 自动行为**：智能的自动动作和对话系统

## 🎭 情绪系统详解

### 四维情绪模型
- **happiness** (快乐度): 0-1，影响积极行为的权重
- **energy** (精力): 0-1，影响活跃行为的权重
- **alertness** (警觉性): 0-1，影响防御行为的权重
- **friendliness** (友好度): 0-1，影响社交行为的权重

### 情绪影响机制
- **行为权重调整**: 情绪状态影响不同行为的选择概率
- **自然衰减**: 情绪值自动回归到默认值
- **事件触发**: 受伤、恢复等事件会改变情绪状态

### 情绪与行为的关系
- **social行为**: 受 friendliness 影响
- **active行为**: 受 energy 影响
- **basic行为**: 基础权重，受整体情绪影响

## ❤️ 生命值系统详解

### 生命值机制
- **初始生命值**: 3/3
- **受伤机制**: 战斗中受到攻击减少1点生命值
- **倒下状态**: 生命值为0时进入倒下状态
- **自动恢复**: 受伤5秒后开始自动恢复

### 生命值与情绪的交互
- **受伤时**: happiness ↓, alertness ↑
- **恢复时**: happiness ↑
- **倒下时**: energy ↓, happiness ↓
- **复活时**: 完全恢复生命值和情绪

## ⚔️ 战斗系统详解

### 战斗流程
1. **点击攻击**: 在战斗模式下点击功夫狗
2. **随机判定**: 40%格挡，60%受击
3. **格挡处理**: 播放格挡动画，50%概率反击
4. **受击处理**: 减少生命值，播放受伤动画
5. **UI更新**: 实时更新生命值和战斗统计

### 战斗统计
- **总攻击**: 累计攻击次数
- **格挡**: 成功格挡次数
- **命中**: 成功命中次数
- **反击**: 反击成功次数

## 🧠 AI行为系统详解

### 行为类型
- **greeting**: 打招呼行为
- **energetic**: 活跃行为（跳跃、踢腿）
- **idle**: 待机行为
- **social**: 社交行为
- **active**: 主动行为
- **basic**: 基础行为

### 行为选择机制
- **权重计算**: 基础权重 × 情绪调整系数
- **随机选择**: 根据权重概率选择行为
- **冷却机制**: 防止重复执行相同行为

## 🎮 全局调试函数

### 情绪系统测试
```javascript
setEmotionalState('happiness', 0.9);  // 设置情绪状态
getEmotionalState();                  // 获取当前情绪
```

### 生命值系统测试
```javascript
getHealthStatus();    // 获取生命值状态
takeDamage(1);       // 造成伤害
heal(0.5);           // 恢复生命值
fullHeal();          // 完全恢复
```

### 动画和行为测试
```javascript
testBodyPartClick('head');    // 测试身体部位点击
testAllBodyParts();          // 测试所有身体部位
```

## 📝 开发说明

### 架构设计原则
- **面向对象**: 角色类负责自身状态管理
- **单一职责**: 每个系统专注于特定功能
- **依赖注入**: 系统间通过角色类进行通信
- **事件驱动**: 基于用户交互和系统事件

### 技术标准
- 使用标准 Three.js 场景架构
- 基于 AnimationMixer 的动画控制
- 明确命名的动画 clip 管理
- 拟人化宠物对话风格
- 事件驱动的交互逻辑
- Shape Keys 面部表情控制

## 🎉 项目完成度

✅ 所有阶段已完成：
- ✅ 阶段一：Three.js 场景搭建
- ✅ 阶段二：动画系统实现
- ✅ 阶段三：说话系统开发
- ✅ 阶段四：交互逻辑添加
- ✅ 阶段五：美术效果优化
- ✅ 阶段六：情绪系统集成
- ✅ 阶段七：生命值系统实现
- ✅ 阶段八：战斗系统完善
- ✅ 阶段九：AI行为系统优化
- ✅ 阶段十：系统架构重构

## 🚀 最新更新

### v2.0 - 角色系统重构 (2025-01-21)
- ✅ **情绪系统迁移**: 从 BehaviorSystem 迁移到 KungFuDog 角色类
- ✅ **生命值系统迁移**: 从 BattleSystem 迁移到 KungFuDog 角色类
- ✅ **面向对象重构**: 角色类成为状态管理中心
- ✅ **自动化管理**: 情绪衰减和生命值自动恢复
- ✅ **系统集成**: 情绪与生命值相互影响
- ✅ **调试功能**: 丰富的全局测试函数

### 核心改进
- **架构优化**: 符合面向对象设计原则
- **状态集中**: 角色相关状态统一管理
- **自动更新**: 统一的 update 方法管理所有系统
- **交互增强**: 情绪和生命值的动态交互

---

**享受与功夫狗的互动吧！体验全新的情绪和生命值系统！🐕‍🦺🥋❤️**
